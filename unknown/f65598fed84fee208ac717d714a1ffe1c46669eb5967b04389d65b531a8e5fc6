package com.link.riderlink.features

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.link.riderlink.ui.extensions.SharedFlowEvents
import com.link.riderlink.ui.extensions.setEvent
import com.link.riderlink.ui.extensions.setState
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

/**
 * MainActivity的ViewModel
 * 管理主界面的UI状态和业务逻辑
 * 使用StateFlow进行响应式状态管理
 */
class MainViewModel : ViewModel() {

    // 状态流
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> get() = _uiState

    // 事件流
    val errorEvents = SharedFlowEvents<String>()

    init {
        initializeState()
    }

    /**
     * 初始化UI状态
     */
    private fun initializeState() {
        // 初始状态
        _uiState.value = MainUiState(isLaunchPageVisible = true, isFabVisible = false)
        // 延迟隐藏启动页
        viewModelScope.launch {
            try {
                delay(2000L)
                _uiState.setState {
                    copy(isLaunchPageVisible = false, isFabVisible = true)
                }
            } catch (e: Exception) {
                errorEvents.setEvent("启动页面隐藏失败: ${e.message}")
            }
        }
    }

    /**
     * 显示锁屏时隐藏FAB
     */
    fun onLockScreenShown() {
        _uiState.setState { copy(isFabVisible = false) }
    }

    /**
     * 锁屏解除时显示FAB
     */
    fun onLockScreenDismissed() {
        _uiState.setState { copy(isFabVisible = true) }
    }

    /**
     * 手动触发启动页面隐藏（用于测试或特殊情况）
     */
    fun hideLaunchPageManually() {
        _uiState.setState { copy(isLaunchPageVisible = false, isFabVisible = true) }
    }

    /**
     * 显示启动页面
     */
    fun showLaunchPage() {
        _uiState.setState { copy(isLaunchPageVisible = true, isFabVisible = false) }
    }
}

/**
 * 主界面UI状态数据类
 */
data class MainUiState(
    val isLaunchPageVisible: Boolean = false,
    val isFabVisible: Boolean = false
) 