package com.link.riderlink.utils.system

import android.text.TextUtils
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger


class AppBackgroundManager private constructor() {
    private var mActivityStated = 0
    private var mLastStartActivityName: String? = null
    private var mListeners: ArrayList<IAppStateChangeListener>? = null
    private var isAppForeground = false
    private val mLastResume = AtomicBoolean(false)
    private val mMultiStart = AtomicInteger(0)
    private var isLockScreen = false

    interface IAppStateChangeListener {
        fun onAppStateChanged(isAppForeground: Boolean)
    }

    private fun callBackState(isAppForeground: Boolean) {
        val it: Iterator<IAppStateChangeListener> = mListeners!!.iterator()
        while (it.hasNext()) {
            it.next().onAppStateChanged(isAppForeground)
        }
    }

    private fun onAppForegroundStateChange(isAppForeground: Boolean) {
        val arrayList = mListeners
        if (arrayList == null || arrayList.isEmpty()) {
            return
        }
        if (!isLockScreen) {
            callBackState(isAppForeground)
        } else {
            isLockScreen = false
        }
    }

    fun isAppOnForeground(): Boolean {
        return isAppForeground
    }


    fun onActivityStarted(str: String) {
        if (!(!TextUtils.isEmpty(str) && str == mLastStartActivityName) && mLastResume.get()) {
            mMultiStart.incrementAndGet()
        }
        mLastStartActivityName = str
        mLastResume.set(true)
        if (!isAppForeground) {
            mActivityStated = STATE_OPEN
            onAppForegroundStateChange(true)
        } else {
            mActivityStated = STATE_RESUMED
        }
        isAppForeground = true
    }


    fun onActivityStopped() {
        if (mMultiStart.get() > 1) {
            mMultiStart.decrementAndGet()
            return
        }
        mLastResume.set(false)
        if (mActivityStated == STATE_RESUMED) {
            mActivityStated = STATE_STOPPED
        } else if (isAppForeground) {
            mMultiStart.set(0)
            isAppForeground = false
            onAppForegroundStateChange(false)
        }
    }

    fun regAppStateListener(iAppStateChangeListener: IAppStateChangeListener?) {
        if (mListeners == null) {
            mListeners = ArrayList()
        }
        if (iAppStateChangeListener == null || mListeners?.contains(iAppStateChangeListener) == true) {
            return
        }
        mListeners?.add(iAppStateChangeListener)
    }

    fun setLockScreen(lockScreen: Boolean) {
        isLockScreen = lockScreen
    }


    companion object {
        private var instance: AppBackgroundManager? = null
        fun getInstance(): AppBackgroundManager {
            if (instance == null) {
                synchronized(AppBackgroundManager::class.java) {
                    if (instance == null) {
                        instance = AppBackgroundManager()
                    }
                }
            }
            return instance!!
        }

        private const val STATE_OPEN = 0
        private const val STATE_RESUMED = 1
        private const val STATE_STOPPED = 2
    }
}