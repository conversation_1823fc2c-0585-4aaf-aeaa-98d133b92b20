package com.link.riderlink.utils.system

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.util.Log
import me.jessyan.autosize.utils.AutoSizeUtils
import java.util.Collections


object ScreenBrightnessUtils {
    const val TAG = "ScreenBrightnessUtils"
    private var mIsScreenOff = false

    private var mScreenBroadcastReceiver: ScreenBroadcastReceiver? = null


    private val mListeners: MutableList<OnScreenStateUpdateListener> =
        Collections.synchronizedList(ArrayList())

    fun addScreenListener(onScreenStateUpdateListener: OnScreenStateUpdateListener) {
        val list = mListeners
        if (list.contains(onScreenStateUpdateListener)) {
            return
        }
        list.add(onScreenStateUpdateListener)
    }


    fun onDestroy() {
        unregisterScreenBroadcastReceiver(AutoSizeUtils.getApplicationByReflect())
    }

    fun registerScreenBroadcastReceiver(context: Context) {
        if (mScreenBroadcastReceiver == null) {
            mScreenBroadcastReceiver = ScreenBroadcastReceiver()
            val intentFilter = IntentFilter()
            intentFilter.addAction("android.intent.action.SCREEN_ON")
            intentFilter.addAction("android.intent.action.SCREEN_OFF")
            intentFilter.addAction("android.intent.action.USER_PRESENT")
            context.registerReceiver(mScreenBroadcastReceiver, intentFilter)
        }
    }


    fun removeScreenListener(onScreenStateUpdateListener: OnScreenStateUpdateListener) {
        mListeners.remove(onScreenStateUpdateListener)
    }

    fun unregisterScreenBroadcastReceiver(context: Context) {
        val screenBroadcastReceiver = mScreenBroadcastReceiver
        if (screenBroadcastReceiver != null) {
            context.unregisterReceiver(screenBroadcastReceiver)
            mScreenBroadcastReceiver = null
        }
    }

    fun whenScreenOff() {
        if (mIsScreenOff) {
            return
        }
        Log.d(TAG, " press power key screen off")
        mIsScreenOff = true
        mListeners.forEach {
            it.whenScreenOff()
        }
    }

    fun whenScreenOn() {
        if (mIsScreenOff) {
            Log.d(TAG, " press power key, screen on ")
            mIsScreenOff = false
        }
        mListeners.forEach {
            it.whenScreenOn()
        }
    }

    fun whenUserPresent() {
        Log.d(TAG, " press power key user present")
        mListeners.forEach {
            it.whenUserPresent()
        }
    }

    fun isScreenOff(): Boolean {
        return mIsScreenOff
    }

    interface OnScreenStateUpdateListener {
        fun whenScreenOff()
        fun whenScreenOn()
        fun whenUserPresent()
    }

    class ScreenBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            when (intent.action) {
                "android.intent.action.SCREEN_ON" -> {
                    whenScreenOn()
                }

                "android.intent.action.SCREEN_OFF" -> {
                    whenScreenOff()
                }

                "android.intent.action.USER_PRESENT" -> {
                    whenUserPresent()
                }
            }
        }
    }
}