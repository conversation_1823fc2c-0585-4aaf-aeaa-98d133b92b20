package com.link.riderlink.ui.theme

import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import com.link.riderlink.ui.theme.core.*
import io.mockk.*
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * 主题系统单元测试
 * 验证主题提供者、资源管理和缓存功能
 */
class ThemeSystemTest {

    private lateinit var context: Context
    private lateinit var resources: Resources
    private lateinit var themeProvider: DefaultThemeProvider

    @Before
    fun setup() {
        context = mockk(relaxed = true)
        resources = mockk(relaxed = true)
        
        every { context.resources } returns resources
        every { context.applicationContext } returns context
        every { context.packageName } returns "com.link.riderlink"
        
        themeProvider = DefaultThemeProvider(context)
    }

    @Test
    fun `test default theme mode is DAY`() {
        assertEquals(ThemeMode.DAY, themeProvider.getCurrentThemeMode())
        assertFalse(themeProvider.isNightMode())
    }

    @Test
    fun `test theme mode switching`() {
        // 切换到夜间模式
        themeProvider.setThemeMode(ThemeMode.NIGHT)
        assertEquals(ThemeMode.NIGHT, themeProvider.getCurrentThemeMode())
        assertTrue(themeProvider.isNightMode())
        
        // 切换回日间模式
        themeProvider.setThemeMode(ThemeMode.DAY)
        assertEquals(ThemeMode.DAY, themeProvider.getCurrentThemeMode())
        assertFalse(themeProvider.isNightMode())
    }

    @Test
    fun `test color resource selection`() {
        val dayColor = 0xFF000000.toInt()
        val nightColor = 0xFFFFFFFF.toInt()
        
        // 日间模式
        themeProvider.setThemeMode(ThemeMode.DAY)
        assertEquals(dayColor, themeProvider.getColorResource(dayColor, nightColor))
        
        // 夜间模式
        themeProvider.setThemeMode(ThemeMode.NIGHT)
        assertEquals(nightColor, themeProvider.getColorResource(dayColor, nightColor))
    }

    @Test
    fun `test drawable resource with cache`() {
        val dayDrawableRes = 100
        val nightDrawableRes = 200
        
        // Mock 资源查找
        every { resources.getResourceEntryName(dayDrawableRes) } returns "test_icon"
        every { resources.getResourceTypeName(dayDrawableRes) } returns "drawable"
        every { 
            resources.getIdentifier("test_icon_night", "drawable", "com.link.riderlink") 
        } returns nightDrawableRes
        
        // 日间模式
        themeProvider.setThemeMode(ThemeMode.DAY)
        assertEquals(dayDrawableRes, themeProvider.getDrawableResource(dayDrawableRes))
        
        // 夜间模式 - 第一次调用
        themeProvider.setThemeMode(ThemeMode.NIGHT)
        assertEquals(nightDrawableRes, themeProvider.getDrawableResource(dayDrawableRes))
        
        // 夜间模式 - 第二次调用（测试缓存）
        assertEquals(nightDrawableRes, themeProvider.getDrawableResource(dayDrawableRes))
        
        // 验证资源查找只调用了一次（缓存生效）
        verify(exactly = 1) { 
            resources.getIdentifier("test_icon_night", "drawable", "com.link.riderlink") 
        }
    }

    @Test
    fun `test theme change listener`() {
        var callbackCount = 0
        var receivedMode: ThemeMode? = null
        
        val listener = object : ThemeChangeListener {
            override fun onThemeChanged(newMode: ThemeMode) {
                callbackCount++
                receivedMode = newMode
            }
        }
        
        themeProvider.registerThemeChangeListener(listener)
        
        // 切换主题应该触发回调
        themeProvider.setThemeMode(ThemeMode.NIGHT)
        assertEquals(1, callbackCount)
        assertEquals(ThemeMode.NIGHT, receivedMode)
        
        // 设置相同模式不应触发回调
        themeProvider.setThemeMode(ThemeMode.NIGHT)
        assertEquals(1, callbackCount)
        
        // 再次切换应该触发回调
        themeProvider.setThemeMode(ThemeMode.DAY)
        assertEquals(2, callbackCount)
        assertEquals(ThemeMode.DAY, receivedMode)
        
        themeProvider.unregisterThemeChangeListener(listener)
    }

    @Test
    fun `test system night mode detection`() {
        val configuration = mockk<Configuration>()
        every { context.resources.configuration } returns configuration
        
        // 测试夜间模式
        configuration.uiMode = Configuration.UI_MODE_NIGHT_YES
        assertTrue(themeProvider.isSystemNightMode())
        
        // 测试日间模式
        configuration.uiMode = Configuration.UI_MODE_NIGHT_NO
        assertFalse(themeProvider.isSystemNightMode())
    }

    @Test
    fun `test theme resource color pair`() {
        val dayColorRes = android.R.color.black
        val nightColorRes = android.R.color.white
        val colorPair = ThemeResource.ColorPair(dayColorRes, nightColorRes)
        
        // Mock color values
        every { context.getColor(dayColorRes) } returns 0xFF000000.toInt()
        every { context.getColor(nightColorRes) } returns 0xFFFFFFFF.toInt()
        
        // 日间模式
        themeProvider.setThemeMode(ThemeMode.DAY)
        assertEquals(dayColorRes, colorPair.getCurrentColorRes(themeProvider))
        assertEquals(0xFF000000.toInt(), colorPair.getCurrentColor(context, themeProvider))
        
        // 夜间模式
        themeProvider.setThemeMode(ThemeMode.NIGHT)
        assertEquals(nightColorRes, colorPair.getCurrentColorRes(themeProvider))
        assertEquals(0xFFFFFFFF.toInt(), colorPair.getCurrentColor(context, themeProvider))
    }

    @Test
    fun `test theme resource drawable pair`() {
        val dayDrawableRes = 100
        val nightDrawableRes = 200
        val drawablePair = ThemeResource.DrawablePair(dayDrawableRes, nightDrawableRes)
        
        // 日间模式
        themeProvider.setThemeMode(ThemeMode.DAY)
        assertEquals(dayDrawableRes, drawablePair.getCurrentDrawableRes(themeProvider))
        
        // 夜间模式
        themeProvider.setThemeMode(ThemeMode.NIGHT)
        assertEquals(nightDrawableRes, drawablePair.getCurrentDrawableRes(themeProvider))
    }

    @Test
    fun `test drawable pair without night resource`() {
        val dayDrawableRes = 100
        val drawablePair = ThemeResource.DrawablePair(dayDrawableRes, null)
        
        // 日间模式
        themeProvider.setThemeMode(ThemeMode.DAY)
        assertEquals(dayDrawableRes, drawablePair.getCurrentDrawableRes(themeProvider))
        
        // 夜间模式（应该降级到日间资源）
        themeProvider.setThemeMode(ThemeMode.NIGHT)
        assertEquals(dayDrawableRes, drawablePair.getCurrentDrawableRes(themeProvider))
    }

    @Test
    fun `test resource not found fallback`() {
        val dayDrawableRes = 100
        
        // Mock 资源查找失败
        every { resources.getResourceEntryName(dayDrawableRes) } throws Resources.NotFoundException()
        
        // 夜间模式下资源查找失败应该返回原始资源
        themeProvider.setThemeMode(ThemeMode.NIGHT)
        assertEquals(dayDrawableRes, themeProvider.getDrawableResource(dayDrawableRes))
    }

    @Test
    fun `test multiple listeners`() {
        var callback1Count = 0
        var callback2Count = 0
        
        val listener1 = object : ThemeChangeListener {
            override fun onThemeChanged(newMode: ThemeMode) { callback1Count++ }
        }
        
        val listener2 = object : ThemeChangeListener {
            override fun onThemeChanged(newMode: ThemeMode) { callback2Count++ }
        }
        
        themeProvider.registerThemeChangeListener(listener1)
        themeProvider.registerThemeChangeListener(listener2)
        
        themeProvider.setThemeMode(ThemeMode.NIGHT)
        
        assertEquals(1, callback1Count)
        assertEquals(1, callback2Count)
        
        // 移除一个监听器
        themeProvider.unregisterThemeChangeListener(listener1)
        themeProvider.setThemeMode(ThemeMode.DAY)
        
        assertEquals(1, callback1Count)  // 不再增加
        assertEquals(2, callback2Count)  // 继续增加
        
        themeProvider.unregisterThemeChangeListener(listener2)
    }

    @Test
    fun `test singleton instance`() {
        val instance1 = DefaultThemeProvider.getInstance(context)
        val instance2 = DefaultThemeProvider.getInstance(context)
        
        assertSame(instance1, instance2)
    }

    @Test
    fun `test backward compatibility constants`() {
        // 测试向后兼容性常量
        assertNotNull(ThemeColors.PRIMARY_BACKGROUND)
        assertNotNull(ThemeColors.PRIMARY_TEXT)
        assertNotNull(ThemeColors.STATUS_SUCCESS)
        assertNotNull(ThemeColors.STATUS_ERROR)
        
        // 验证它们指向相同的资源
        assertEquals(ThemeResources.PRIMARY_BACKGROUND, ThemeColors.PRIMARY_BACKGROUND)
        assertEquals(ThemeResources.PRIMARY_TEXT, ThemeColors.PRIMARY_TEXT)
    }
} 