package com.link.riderlink.features.search

import com.link.rideramap.api.dto.PoiAddress
import com.link.riderlink.core.database.model.SearchAddress
import com.link.riderlink.core.shared.search.ui.BaseSearchViewEvent
import com.link.riderlink.core.shared.search.ui.BaseSearchViewState

/**
 * 高德地图搜索模块的向后兼容类型别名和扩展
 * <AUTHOR>
 * @date 2022/8/9
 */

// 向后兼容的类型别名，使用共享的基础类
typealias SearchViewState = BaseSearchViewState
typealias SearchViewEvent = BaseSearchViewEvent

// 扩展SearchViewAction以支持POI搜索（高德地图特有功能）
sealed class SearchViewAction {
    /**
     * 基础搜索操作
     */
    data class Search(val keyword: String) : SearchViewAction()
    data class InsertHistory(val searchAddress: SearchAddress) : SearchViewAction()
    data class DeleteHistory(val searchAddress: SearchAddress) : SearchViewAction()
    object LoadHistories : SearchViewAction()
    
    /**
     * POI搜索操作（高德地图特有）
     */
    data class SearchPOI(val poiAddress: PoiAddress) : SearchViewAction()
}