package com.link.riderlink.features.search.data.repository

import com.link.riderlink.core.shared.search.data.source.local.LocalSearchDataSource
import com.link.riderlink.core.database.model.SearchAddress
import com.link.riderlink.core.shared.search.domain.repository.SearchRepository
import kotlinx.coroutines.flow.Flow

class SearchRepositoryImpl(
    private val localSearchDataSource: LocalSearchDataSource
) : SearchRepository {
    override suspend fun insertHistory(history: SearchAddress) {
        localSearchDataSource.insertHistory(history)
    }

    override suspend fun deleteHistory(history: SearchAddress) {
        localSearchDataSource.deleteHistory(history)
    }

    override fun loadHistories(): Flow<List<SearchAddress>> = localSearchDataSource.loadHistories()
}