package com.link.riderlink.features.home

import android.annotation.SuppressLint
import android.app.ActionBar.LayoutParams
import android.app.Activity.RESULT_OK
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.PopupMenu
import android.widget.TextView
import androidx.activity.addCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.graphics.drawable.toDrawable
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.isGone
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.hjq.toast.ToastUtils
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanOptions
import com.link.riderdvr.utils.mainScope
import com.link.riderlink.R
import com.link.riderlink.RiderLink
import com.link.riderlink.databinding.FragmentHomeBinding
import com.link.riderlink.ui.extensions.IS_AGREE_POLICY
import com.link.riderlink.ui.extensions.IS_BLUETOOTH_DENIED
import com.link.riderlink.ui.extensions.IS_LOCATION_DENIED
import com.link.riderlink.utils.system.PermissionsUtil
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.utils.system.connectPermissions
import com.link.riderlink.ui.extensions.getBoolean
import com.link.riderlink.utils.system.locationPermissions
import com.link.riderlink.ui.extensions.navigate
import com.link.riderlink.ui.extensions.observeEvent
import com.link.riderlink.ui.extensions.observeState
import com.link.riderlink.ui.extensions.putBoolean
import com.link.riderlink.ui.components.home.ConnectInfoMange
import com.link.riderservice.api.dto.BleStatus
import com.link.riderservice.api.dto.WifiStatus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.core.content.edit
import com.link.riderlink.view.CustomScannerActivity
import com.link.riderservice.BuildConfig
import com.link.riderservice.feature.connection.ble.BleDevice


/**
 * <AUTHOR>
 * @date 2022/12/6
 */

class HomeFragment : Fragment() {
    private lateinit var launcher: ActivityResultLauncher<Intent>
    private val homeViewModel: HomeViewModel by viewModels()
    private val deviceViewModel: DeviceViewModel by activityViewModels()
    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!

    private var privacyDialog: Dialog? = null

    private var connectDialog: Dialog? = null

    private var connectListDialog: Dialog? = null

    private var isHomePage: Boolean = true

    private val click = { it: BleDevice ->
        deviceViewModel.dispatch(BleViewAction.BleItemClicked(it))
    }


    private val longClick = { view: View, item: BleDevice ->
        myPopupMenu(view, item)
    }

    private val adapter: DeviceAdapter by lazy {
        DeviceAdapter(requireContext(), click, longClick)
    }

    private lateinit var barcodeLauncher: ActivityResultLauncher<ScanOptions>

    override fun onAttach(context: Context) {
        super.onAttach(context)
        Log.d(TAG, "onAttach:")
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            if (connectDialog?.isShowing == true) {
                connectDialog?.dismiss()
            }
        }
        launcher =
            requireActivity().registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
                Log.d(TAG, "launcher: $it")
                Log.d(TAG, "launcher: ${it.resultCode}: ${it.resultCode}")
                if (it.resultCode == RESULT_OK) {
                    val mediaProjectionManager: MediaProjectionManager =
                        context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
                    val mediaProjection =
                        mediaProjectionManager.getMediaProjection(it.resultCode, it.data!!)
                    RiderLink.instance.setMediaProjection(mediaProjection)
                } else {
                    ToastUtils.show(getString(R.string.no_mediaprojection_permission))
                    RiderLink.instance.stopMirror()
                }
            }

        barcodeLauncher = registerForActivityResult(ScanContract()) { result ->
            if (result.contents == null) {
                ToastUtils.show("Cancelled")
            } else {
                Log.d(TAG, "scan ${result.contents}")
                val sharedPref =
                    context.getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
                sharedPref.edit {
                    putString("scan_ble_address", result.contents)
                }
                start()
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }

    @SuppressLint("SetTextI18n")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        homeViewModel.viewEvents.observeEvent(this) { event ->
            when (event) {
                is HomeViewEvent.UpdateLocation -> {
                    if (isHomePage) {
                        binding.mainLocationTv.text = event.locationInfo.address
                    }
                }

                is HomeViewEvent.ChangeIdleText -> {
                    if (isHomePage) {
                        binding.connectStateText.text = ConnectInfoMange.getIdleText()
                    }
                }

            }
        }

        homeViewModel.viewStates.observeState(viewLifecycleOwner, HomeViewState::geographyInfo) {
            binding.tvWeather.text = it.weather.city.plus("·").plus(it.weather.weather).plus("·")
                .plus(it.weather.temperature).plus(getString(R.string.humidity))
                .plus(it.weather.humidity)
        }

        homeViewModel.viewStates.observeState(viewLifecycleOwner, HomeViewState::status) {
            binding.connectStateText.text = ConnectInfoMange.updateConnectState(context, it)
            if (it.btStatus is BleStatus.DeviceConnecting) {
                connectDialog?.dismiss()
                binding.buttonScan.visibility = View.GONE
            }

            if ((it.btStatus is BleStatus.DeviceConnected) && (it.wifiStatus is WifiStatus.DeviceConnected)) {
                binding.connectStatePoint.setImageResource(R.drawable.connected_point)
                binding.connectStateText.setTextColor(
                    ThemeColors.STATUS_SUCCESS.getCurrentColorInt(requireContext())
                )
                binding.buttonConnect.visibility = View.GONE
                binding.connectStateSign.visibility = View.VISIBLE
                binding.buttonDisconnect.visibility = View.VISIBLE
                binding.buttonMirror.visibility = View.VISIBLE
                binding.buttonScan.visibility = View.GONE
                binding.mainLocationBitmap.setImageResource(R.drawable.location_connected)
                binding.mainButtonMap.setBitImage(if (homeViewModel.viewStates.value.config.isSupportDvr) R.drawable.main_tool_bitmap_map1 else R.drawable.main_tool_bitmap_map3)
                binding.mainButtonMap.isClickAble(false)
                binding.mainButtonDvr.setBitImage(R.drawable.main_tool_bitmap_dvr2)
                binding.mainButtonDvr.isClickAble(false)
                connectDialog?.dismiss()
            }
            if (it.btStatus is BleStatus.DeviceDisconnected && it.wifiStatus is WifiStatus.DeviceDisconnected) {
                binding.connectStatePoint.setImageResource(
                    ThemeManager.autoChangeInt(
                        R.drawable.un_connect_point,
                        R.drawable.un_connect_point_night
                    )
                )
                binding.connectStateText.setTextColor(
                    ThemeColors.STATUS_ERROR.getCurrentColorInt(requireContext())
                )
                binding.connectStateSign.visibility = View.GONE
                binding.buttonConnect.visibility = View.VISIBLE
                binding.buttonDisconnect.visibility = View.GONE
                binding.buttonMirror.visibility = View.GONE
                binding.buttonScan.visibility = View.VISIBLE
                binding.mainLocationBitmap.setImageResource(R.drawable.location_unconnect)
                binding.mainButtonMap.setBitImage(if (homeViewModel.viewStates.value.config.isSupportDvr) R.drawable.main_tool_bitmap_map1 else R.drawable.main_tool_bitmap_map3)
                binding.mainButtonMap.isClickAble(true)
                binding.mainButtonDvr.setBitImage(R.drawable.main_tool_bitmap_dvr1)
                binding.mainButtonDvr.isClickAble(true)
            }
        }

        homeViewModel.viewStates.observeState(viewLifecycleOwner, HomeViewState::isMirror) {
            binding.buttonMirror.setText(if (it) getString(R.string.stop_mirror) else getString(R.string.mirror))
            binding.buttonMirror.setTextColor(
                if (it) ThemeManager.autoChangeInt(
                    R.color.blue_tx,
                    R.color.white_tx
                ) else R.color.black_tx
            )
            binding.buttonMirror.setBackgroundImage(
                if (it) ThemeManager.autoChangeInt(
                    R.drawable.button_connect_white_select,
                    R.drawable.button_connect_blue_select
                ) else R.drawable.button_connect_white_select
            )
            binding.buttonMirror.setBitmap(
                if (it) ThemeManager.getCurrentThemeRes(
                    requireContext(),
                    R.drawable.mirrored
                ) else R.drawable.mirror
            )
        }

        homeViewModel.viewStates.observeState(viewLifecycleOwner, HomeViewState::config) {
            changeView(it.isSupportNavi, it.isSupportDvr)
        }

        binding.buttonConnect.setOnClickListener {
            connectListDialog = showConnectListDialog()
        }
        binding.buttonDisconnect.setOnClickListener {
            connectDialog = showConnectDialog(false)
        }
        binding.buttonMirror.setOnClickListener {
            Log.e(TAG, "onViewCreated: buttonMirror")
            homeViewModel.dispatch(HomeViewAction.Mirror(launcher))
        }
        binding.menuMore.setOnClickListener {
            Log.e(TAG, "onViewCreated: menuMore")
            navigate(R.id.action_homeFragment_to_settingFragment)

        }
        binding.mainButtonMap.setOnClickListener {
            navigate(R.id.action_homeFragment_to_mapFragment)
        }
        binding.mainButtonDvr.setOnClickListener {
            navigate(R.id.action_homeFragment_to_dvrFragment)
        }
        binding.mainButtonQqM.setOnClickListener {
            Log.e(TAG, "onViewCreated: mainButtonQqM")

        }

        binding.buttonScan.setOnClickListener {
            val scanOptions = ScanOptions().apply {
                setCaptureActivity(CustomScannerActivity::class.java)
                setDesiredBarcodeFormats(ScanOptions.QR_CODE)
                setOrientationLocked(true)
                setPrompt("请对准二维码")
            }
            barcodeLauncher.launch(scanOptions)
        }
        mainScope.launch(Dispatchers.Main) {
            delay(2000L)
            privacyDialog =
                showRuleDialog(resources.getString(R.string.policy_title), R.color.link)
        }
        ThemeManager.registerThemeChangeListener(themeCallback)
        initTheme()

        Log.d(TAG, "onViewCreated:${BuildConfig.DEBUG}")

        isHomePage = true
    }


    val themeCallback = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }

    private fun changeView(isSupportNavi: Boolean, isSupportDvr: Boolean) {

        if (!isSupportNavi && !isSupportDvr) {
            binding.mainButtonMap.visibility = View.GONE
            binding.mainButtonDvr.visibility = View.GONE
            binding.mainButtonQqM.visibility = View.GONE
            return
        }
        if (isSupportNavi && isSupportDvr) {
            binding.mainButtonMap.visibility = View.VISIBLE
            binding.mainButtonDvr.visibility = View.VISIBLE
            binding.mainButtonQqM.visibility = View.VISIBLE
            binding.mainButtonMap.setBitImage(R.drawable.main_tool_bitmap_map1)
            val set = ConstraintSet().apply {
                constrainWidth(binding.mainLocationBitmap.id, 84)
                constrainHeight(binding.mainLocationBitmap.id, 84)
                connect(
                    binding.mainLocationBitmap.id,
                    ConstraintSet.RIGHT,
                    binding.mainButtonMap.id,
                    ConstraintSet.RIGHT,
                    183
                )
                connect(
                    binding.mainLocationBitmap.id,
                    ConstraintSet.TOP,
                    binding.mainButtonMap.id,
                    ConstraintSet.TOP,
                    69
                )
                constrainWidth(binding.mainButtonMap.id, LayoutParams.MATCH_PARENT)
                constrainHeight(binding.mainButtonMap.id, 318)
            }
            set.applyTo(binding.mainToolBox)
        }
        if (isSupportNavi && !isSupportDvr) {
            binding.mainButtonMap.visibility = View.VISIBLE
            binding.mainButtonDvr.visibility = View.GONE
            binding.mainButtonQqM.visibility = View.GONE
            binding.mainButtonMap.setBitImage(R.drawable.main_tool_bitmap_map3)
            val set = ConstraintSet().apply {
                constrainWidth(binding.mainLocationBitmap.id, 120)
                constrainHeight(binding.mainLocationBitmap.id, 120)
                connect(
                    binding.mainLocationBitmap.id,
                    ConstraintSet.LEFT,
                    binding.mainButtonMap.id,
                    ConstraintSet.LEFT,
                    450
                )
                connect(
                    binding.mainLocationBitmap.id,
                    ConstraintSet.TOP,
                    binding.mainButtonMap.id,
                    ConstraintSet.TOP,
                    198
                )
                constrainWidth(binding.mainButtonMap.id, LayoutParams.MATCH_PARENT)
                constrainHeight(binding.mainButtonMap.id, 510)
            }
            set.applyTo(binding.mainToolBox)
        }
        requestLocation()
    }

    private fun requestLocation() {
        XXPermissions.with(requireContext())
            .permission(Permission.ACCESS_FINE_LOCATION)
            .permission(Permission.ACCESS_COARSE_LOCATION)
            .request { _: List<String?>?, all: Boolean ->
                if (all) {
                    homeViewModel.dispatch(HomeViewAction.StartLocation)
                } else {
                    ToastUtils.show(getString(R.string.no_permission_message))
                }
            }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        privacyDialog?.takeIf { it.isShowing }?.apply { dismiss() }
        isHomePage = false
        _binding = null
        Log.d(TAG, "onDestroyView: ")
    }

    override fun onDestroy() {
        super.onDestroy()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        Log.d(TAG, "onDestroy: ")
    }

    private fun showConnectDialog(isConnect: Boolean): Dialog? {
        if (!isHomePage) {
            return null
        }
        Log.e(TAG, "showConnectDialog: $isHomePage")

        if (connectDialog?.isShowing == true && isConnect || connectListDialog?.isShowing == true) {
            return connectDialog
        }

        homeViewModel.viewStates.value.status.let {
            if (it.btStatus is BleStatus.DeviceConnected && it.wifiStatus is WifiStatus.DeviceConnected && isConnect) {
                return connectDialog
            }
        }
        val dialog = Dialog(requireContext(), R.style.POLICY_DIALOG)
        dialog.setContentView(R.layout.connect_dialog)
        dialog.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())

        val window = dialog.window;
        window?.setGravity(Gravity.BOTTOM)
        val lp = window?.attributes;
        lp?.width = LayoutParams.MATCH_PARENT
        lp?.y = 60
        window?.attributes = lp;

        dialog.show()
        val tvTitle = dialog.findViewById<TextView>(R.id.connect_dialog_title)
        val tvContext = dialog.findViewById<TextView>(R.id.connect_dialog_content)
        val bCancel = dialog.findViewById<ConstraintLayout>(R.id.connect_dialog_button)
        val bCancel2 = dialog.findViewById<ConstraintLayout>(R.id.connect_dialog_button_cancel)
        val bOk = dialog.findViewById<ConstraintLayout>(R.id.connect_dialog_button_ok)
        val dialogBackground = dialog.findViewById<View>(R.id.connect_dialog_background_img)
        dialogBackground.setBackgroundResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                R.drawable.connect_dialog_background
            )
        )
        bCancel.setOnClickListener {
            dialog.dismiss()
        }
        bCancel2.setOnClickListener {
            dialog.dismiss()
        }
        bOk.setOnClickListener {
            homeViewModel.dispatch(HomeViewAction.Disconnect)
            dialog.dismiss()
        }
        if (isConnect) {
            tvTitle.text = getString(R.string.connected_dialog_title)
            tvContext.text = getString(R.string.connected_dialog_content)
            bCancel.visibility = View.VISIBLE
            bCancel2.visibility = View.GONE
            bOk.visibility = View.GONE
        } else {
            tvTitle.text = getString(R.string.disconnected_dialog_title)
            tvContext.text = getString(R.string.disconnected_dialog_content)
            bCancel.visibility = View.GONE
            bCancel2.visibility = View.VISIBLE
            bOk.visibility = View.VISIBLE
        }

        ThemeManager.registerThemeChangeListener(object : ThemeManager.OnThemeChangeListener() {
            override fun onThemeChanged() {
                dialogBackground.setBackgroundResource(
                    ThemeManager.getCurrentThemeRes(
                        requireContext(),
                        R.drawable.connect_dialog_background
                    )
                )
            }

        })
        return dialog
    }

    private fun showConnectListDialog(): Dialog? {
        if (connectDialog?.isShowing == true) {
            return connectDialog
        }
        val dialog = BottomSheetDialog(requireContext(), R.style.ConnectDialog)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.connect_list_dialog)
        val window = dialog.window!!
        window.setWindowAnimations(R.style.BottomDialog_Animation)
        WindowCompat.setDecorFitsSystemWindows(window, false)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            window.isNavigationBarContrastEnforced = false
        }
        window.navigationBarColor = Color.TRANSPARENT
        window.statusBarColor = Color.TRANSPARENT

        ViewCompat.setOnApplyWindowInsetsListener(window.decorView) { v, insets ->
            v.setPadding(
                0,
                insets.systemWindowInsetTop,
                0,
                0,
            )
            insets.consumeSystemWindowInsets()
        }
        dialog.show()
        val bStart = dialog.findViewById<ConstraintLayout>(R.id.connect_list_dialog_button)
        val imgStart = dialog.findViewById<ImageView>(R.id.img_dialog_button)
        val tvStart = dialog.findViewById<TextView>(R.id.tv_dialog_button)
        val tvTitle = dialog.findViewById<TextView>(R.id.connect_list_dialog_title)
        val tvContent = dialog.findViewById<TextView>(R.id.connect_list_dialog_content)
        val bRe = dialog.findViewById<ConstraintLayout>(R.id.connect_list_dialog_button_re)
        val imgCar = dialog.findViewById<ImageView>(R.id.connect_list_dialog_bitmap)
        val bBack1 = dialog.findViewById<ImageView>(R.id.connect_list_dialog_cancel)
        val bBack2 = dialog.findViewById<ConstraintLayout>(R.id.connect_list_dialog_button_cancel)
        val deviceList = dialog.findViewById<RecyclerView>(R.id.connect_dialog_list)
        val dialogBackground =
            dialog.findViewById<ImageView>(R.id.connect_dialog_list_background_img)
        tvContent?.setTextColor(
            resources.getColor(
                ThemeManager.autoChangeInt(
                    R.color.grey_tx,
                    R.color.blue_tx
                ), null
            )
        )
        tvTitle?.setTextColor(
            resources.getColor(
                ThemeManager.autoChangeInt(
                    R.color.black_tx,
                    R.color.blue_tx
                ), null
            )
        )
        val callback = object : ThemeManager.OnThemeChangeListener() {
            @SuppressLint("NotifyDataSetChanged")
            override fun onThemeChanged() {
                tvContent?.setTextColor(
                    resources.getColor(
                        ThemeManager.autoChangeInt(
                            R.color.grey_tx,
                            R.color.blue_tx
                        ), null
                    )
                )
                tvTitle?.setTextColor(
                    resources.getColor(
                        ThemeManager.autoChangeInt(
                            R.color.black_tx,
                            R.color.blue_tx
                        ), null
                    )
                )
                dialogBackground?.setImageResource(
                    ThemeManager.getCurrentThemeRes(
                        requireContext(),
                        R.drawable.connect_list_background
                    )
                )
                deviceList?.setBackgroundResource(
                    ThemeManager.getCurrentThemeRes(
                        requireContext(),
                        R.drawable.connect_dialog_list_background
                    )
                )
                adapter.notifyDataSetChanged()
            }

        }
        dialogBackground?.setImageResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                R.drawable.connect_list_background
            )
        )
        deviceList?.setBackgroundResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                R.drawable.connect_dialog_list_background
            )
        )

        deviceList?.layoutManager = LinearLayoutManager(activity)
        deviceList?.adapter = adapter
        deviceViewModel.viewStates.let { states ->
            states.observeState(viewLifecycleOwner, DeviceViewState::bleList) {
                adapter.submitList(it)
            }
        }

        homeViewModel.viewStates.observeState(viewLifecycleOwner, HomeViewState::isWifiOpen) {
            if (it) {
                imgCar?.setImageResource(R.drawable.wifi_ble)
            } else {
                imgCar?.setImageResource(R.drawable.ble_only)
            }
        }
        deviceViewModel.viewEvents.observeEvent(this) {
            when (it) {
                is BleViewEvent.ConnectStatusChange -> {
                    adapter.currentList.forEachIndexed { index, _ ->
                        adapter.notifyItemChanged(index)
                    }
                }
            }
        }

        homeViewModel.viewStates.observeState(viewLifecycleOwner, HomeViewState::status) {
            binding.connectStateText.text = ConnectInfoMange.updateConnectState(context, it)
            if ((it.btStatus is BleStatus.DeviceConnected) && (it.wifiStatus is WifiStatus.DeviceConnected)) {
                bStart?.visibility = View.GONE
                bRe?.visibility = View.VISIBLE
                bBack2?.visibility = View.VISIBLE
            }
        }
        bStart?.setOnClickListener {
            homeViewModel.dispatch(HomeViewAction.StartScan(false))
            imgStart?.alpha = 0.5F
            tvStart?.text = getString(R.string.search_text)
            bStart.isClickable = false
            imgCar?.visibility = View.GONE
            deviceList?.visibility = View.VISIBLE
            tvContent?.visibility = View.GONE

        }
        if (homeViewModel.viewStates.value.status.btStatus is BleStatus.DeviceConnected) {
            imgStart?.alpha = 0.5F
            tvStart?.text = getString(R.string.search_text)
            bStart?.isClickable = false
            imgCar?.visibility = View.GONE
            deviceList?.visibility = View.VISIBLE
            tvContent?.visibility = View.GONE
        }
        bRe?.setOnClickListener {
            homeViewModel.dispatch(HomeViewAction.StartScan(false))
        }
        bBack1?.setOnClickListener {
            dialog.dismiss()
        }
        bBack2?.setOnClickListener {
            ThemeManager.unregisterThemeChangeListener(callback)
            dialog.dismiss()
        }
        ThemeManager.registerThemeChangeListener(callback)
        return dialog
    }

    private fun showRuleDialog(title: String?, tagColor: Int): Dialog? {
        if (requireContext().getBoolean(IS_AGREE_POLICY)) {
            start()
            connectDialog = showConnectDialog(true)
            return null
        }
        val policyText = getString(R.string.policy_text)
        val dialog = Dialog(requireContext(), R.style.POLICY_DIALOG)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.privacy_first_dialog)
        dialog.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        dialog.show()

        val window = dialog.window;
        val lp = window?.attributes;
        lp?.width = LayoutParams.MATCH_PARENT
        window?.attributes = lp;

        val tvOk = dialog.findViewById<ConstraintLayout>(R.id.privacy_dialog_button_ok)
        val tvCancel = dialog.findViewById<ConstraintLayout>(R.id.privacy_dialog_button_cancel)
        val tvTitle = dialog.findViewById<TextView>(R.id.privacy_dialog_title)
        val tvText = dialog.findViewById<TextView>(R.id.privacy_dialog_content)
        val dialogBackground = dialog.findViewById<View>(R.id.privacy_dialog_background_img)
        val dialogBackgroundTv = dialog.findViewById<View>(R.id.privacy_dialog_text_background)
        dialogBackground.setBackgroundResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                R.drawable.privacy_dialog_background
            )
        )
        dialogBackgroundTv.setBackgroundResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                R.drawable.privacy_dialog_text_background
            )
        )
        tvTitle.setTextColor(
            resources.getColor(
                ThemeManager.autoChangeInt(
                    R.color.black_tx,
                    R.color.blue_tx
                ), null
            )
        )
        tvText.setTextColor(
            resources.getColor(
                ThemeManager.autoChangeInt(
                    R.color.grey_tx,
                    R.color.white_tx
                ), null
            )
        )

        tvTitle.text = title
        val tag1 = "《"
        val tag2 = "》"
        val firstIndex = policyText.indexOf(tag1)
        val secondIndex = policyText.indexOf(tag2) + 1
        val style = SpannableStringBuilder()
        style.append(policyText)
        val clickableSpanOne: ClickableSpan = object : ClickableSpan() {
            override fun onClick(v: View) {
                dialog.dismiss()
                navigate(R.id.action_homeFragment_to_privacyFragment)
            }
        }
        style.setSpan(
            clickableSpanOne, firstIndex, secondIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        tvText.text = style
        val foregroundColorSpanOne = ForegroundColorSpan(resources.getColor(tagColor, null))
        style.setSpan(
            foregroundColorSpanOne, firstIndex, secondIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        tvText.movementMethod = LinkMovementMethod.getInstance()
        tvText.text = style
        tvOk.setOnClickListener {
            lifecycleScope.launch {
                requireContext().putBoolean(IS_AGREE_POLICY, true)
            }
            start()
            dialog.dismiss()
        }
        tvCancel.setOnClickListener {
            dialog.dismiss()
            showRuleRefusedDialog()
        }
        ThemeManager.registerThemeChangeListener(object : ThemeManager.OnThemeChangeListener() {
            override fun onThemeChanged() {
                dialogBackground.setBackgroundResource(
                    ThemeManager.getCurrentThemeRes(
                        requireContext(),
                        R.drawable.privacy_dialog_background
                    )
                )
                dialogBackgroundTv.setBackgroundResource(
                    ThemeManager.getCurrentThemeRes(
                        requireContext(),
                        R.drawable.privacy_dialog_text_background
                    )
                )
                tvText.setTextColor(
                    resources.getColor(
                        ThemeManager.autoChangeInt(
                            R.color.grey_tx,
                            R.color.white_tx
                        ), null
                    )
                )
                tvTitle.setTextColor(
                    resources.getColor(
                        ThemeManager.autoChangeInt(
                            R.color.black_tx,
                            R.color.blue_tx
                        ), null
                    )
                )
            }

        })
        return dialog
    }

    private fun showRuleRefusedDialog(): Dialog {
        val policyText = getString(R.string.policy_refused_text)
        val dialog = Dialog(requireContext(), R.style.POLICY_DIALOG)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.privacy_refused_dialog)
        dialog.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        dialog.show()
        val tvOk = dialog.findViewById<ConstraintLayout>(R.id.privacy_dialog_button_refused_ok)
        val tvCancel =
            dialog.findViewById<ConstraintLayout>(R.id.privacy_dialog_button_refused_exit)
        val tvTitle = dialog.findViewById<TextView>(R.id.privacy_dialog_refused_title)
        val tvText = dialog.findViewById<TextView>(R.id.privacy_dialog_refused_content)
        val dialogBackground = dialog.findViewById<View>(R.id.privacy_dialog_refused_background)
        tvText.text = getString(R.string.policy_refused_title)
        dialogBackground.setBackgroundResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                R.drawable.privacy_dialog_refused_background
            )
        )
        tvTitle.setTextColor(
            resources.getColor(
                ThemeManager.autoChangeInt(
                    R.color.black_tx,
                    R.color.blue_tx
                ), null
            )
        )
        tvText.setTextColor(
            resources.getColor(
                ThemeManager.autoChangeInt(
                    R.color.grey_tx,
                    R.color.white_tx
                ), null
            )
        )
        val tag1 = "《"
        val tag2 = "》"
        val firstIndex = policyText.indexOf(tag1)
        val secondIndex = policyText.indexOf(tag2) + 1
        val style = SpannableStringBuilder()
        style.append(policyText)
        val clickableSpanOne: ClickableSpan = object : ClickableSpan() {
            override fun onClick(v: View) {
                dialog.dismiss()
                navigate(R.id.action_homeFragment_to_privacyFragment)
            }
        }
        style.setSpan(
            clickableSpanOne, firstIndex, secondIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        tvText.text = style
        val foregroundColorSpanOne = ForegroundColorSpan(resources.getColor(R.color.link, null))
        style.setSpan(
            foregroundColorSpanOne, firstIndex, secondIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        tvText.movementMethod = LinkMovementMethod.getInstance()
        tvText.text = style
        tvOk.setOnClickListener {
            lifecycleScope.launch {
                requireContext().putBoolean(IS_AGREE_POLICY, true)
            }
            start()
            dialog.dismiss()
        }
        tvCancel.setOnClickListener {
            lifecycleScope.launch {
                requireContext().putBoolean(IS_AGREE_POLICY, false)
            }
            dialog.dismiss()
            requireActivity().finish()
        }
        ThemeManager.registerThemeChangeListener(object : ThemeManager.OnThemeChangeListener() {
            override fun onThemeChanged() {
                tvText.setTextColor(
                    resources.getColor(
                        ThemeManager.autoChangeInt(
                            R.color.grey_tx,
                            R.color.white_tx
                        ), null
                    )
                )
                tvTitle.setTextColor(
                    resources.getColor(
                        ThemeManager.autoChangeInt(
                            R.color.black_tx,
                            R.color.blue_tx
                        ), null
                    )
                )
                dialogBackground.setBackgroundResource(
                    ThemeManager.getCurrentThemeRes(
                        requireContext(),
                        R.drawable.privacy_dialog_refused_background
                    )
                )
            }

        })
        return dialog
    }

    private fun start() {
        if (XXPermissions.isGranted(requireContext(), locationPermissions)) {
            lifecycleScope.launch {
                requireContext().putBoolean(IS_LOCATION_DENIED, false)
            }
        }

        if (XXPermissions.isGranted(requireContext(), connectPermissions)) {
            lifecycleScope.launch {
                requireContext().putBoolean(IS_BLUETOOTH_DENIED, false)
            }
        }

        if (XXPermissions.isGranted(requireContext(), connectPermissions, locationPermissions)) {
            homeViewModel.dispatch(HomeViewAction.StartScan())
        } else {
            val listener = {
                XXPermissions.with(requireContext())
                    .permission(connectPermissions)
                    .permission(locationPermissions)
                    .request(object : OnPermissionCallback {
                        override fun onGranted(
                            permissions: MutableList<String>,
                            allGranted: Boolean
                        ) {
                            if (allGranted) {
                                homeViewModel.dispatch(HomeViewAction.StartScan())
                                connectDialog = showConnectDialog(true)
                            }
                        }

                        override fun onDenied(
                            permissions: MutableList<String>,
                            doNotAskAgain: Boolean
                        ) {
                            super.onDenied(permissions, doNotAskAgain)
                            if (!XXPermissions.isGranted(requireContext(), locationPermissions)) {
                                lifecycleScope.launch {
                                    requireContext().putBoolean(IS_LOCATION_DENIED, true)
                                }
                            }
                            if (!XXPermissions.isGranted(requireContext(), connectPermissions)) {
                                lifecycleScope.launch {
                                    requireContext().putBoolean(IS_BLUETOOTH_DENIED, true)
                                }
                            }
                        }
                    })
            }
            PermissionsUtil.showRequestPermissionDialog(requireContext(), listener)
        }
    }

    private fun initTheme() {
        if (binding.connectStateSign.isGone) {
            binding.connectStateText.setTextColor(
                ThemeColors.STATUS_ERROR.getCurrentColorInt(requireContext())
            )
        } else {
            binding.connectStateText.setTextColor(
                ThemeColors.STATUS_SUCCESS.getCurrentColorInt(requireContext())
            )
        }
        homeViewModel.viewStates.value.isMirror.let {
            binding.buttonMirror.setTextColor(
                if (it) ThemeManager.autoChangeInt(
                    R.color.blue_tx,
                    R.color.white_tx
                ) else R.color.black_tx
            )
            binding.buttonMirror.setBackgroundImage(
                if (it) ThemeManager.autoChangeInt(
                    R.drawable.button_connect_white_select,
                    R.drawable.button_connect_blue_select
                ) else R.drawable.button_connect_white_select
            )
            binding.buttonMirror.setBitmap(
                if (it) ThemeManager.getCurrentThemeRes(
                    requireContext(),
                    R.drawable.mirrored
                ) else R.drawable.mirror
            )
        }
        binding.connectStateSign.setImageResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                R.drawable.sign
            )
        )
        binding.menuMore.setImageResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                R.drawable.main_menu
            )
        )
        binding.mainButtonMap.onThemeChange(
            ThemeManager.isNightMode(requireContext()),
            requireContext()
        )
        binding.mainButtonDvr.onThemeChange(
            ThemeManager.isNightMode(requireContext()),
            requireContext()
        )
        binding.mainButtonQqM.onThemeChange(
            ThemeManager.isNightMode(requireContext()),
            requireContext()
        )
        binding.homeViewRoot.setBackgroundResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                R.drawable.home_page_background
            )
        )
    }

    private fun myPopupMenu(v: View, bleItem: BleDevice) {
        val popupMenu = PopupMenu(requireActivity(), v);
        popupMenu.menuInflater.inflate(R.menu.device_menu, popupMenu.menu);
        popupMenu.setOnMenuItemClickListener {
            if (it.itemId == R.id.disconnect) {
                if (bleItem.device.address == RiderLink.instance.getCurrentConnectDevice()?.device?.address) {
                    deviceViewModel.dispatch(BleViewAction.Disconnect)
                };
            }
            return@setOnMenuItemClickListener true
        }
        //显示菜单
        popupMenu.show()
    }

    companion object {
        private const val TAG = "HomeFragment"
    }
}