package com.link.riderlink.features.home

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.link.riderlink.RiderLink
import com.link.riderlink.ui.extensions.SharedFlowEvents
import com.link.riderlink.ui.extensions.setEvent
import com.link.riderlink.ui.extensions.setState
import com.link.riderservice.api.callback.RiderServiceCallback
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.feature.connection.ble.BleDevice
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlin.collections.toList

/**
 * <AUTHOR>
 * @date 2022/6/29
 * @desc 设备列表 ViewModel
 */

class DeviceViewModel(application: Application) : AndroidViewModel(application) {

    private val _viewStates = MutableStateFlow(DeviceViewState())

    val viewStates = _viewStates.asStateFlow()

    private val _viewEvents = SharedFlowEvents<BleViewEvent>()

    val viewEvents = _viewEvents.asSharedFlow()

    fun dispatch(bleViewAction: BleViewAction) {
        when (bleViewAction) {
            is BleViewAction.BleItemClicked -> {
                RiderLink.instance.connect(bleViewAction.bleDevice)
            }
            is BleViewAction.Disconnect -> {
                RiderLink.instance.disconnect()
            }
        }
    }

    //提供订阅发布的模式
    private val riderServiceCallback = object : RiderServiceCallback() {
        override fun onScanResult(bleDevices: List<BleDevice>) {
            _viewStates.setState {
                copy(bleList = bleDevices.toList())
            }
        }

        override fun onScanning() {
            _viewStates.setState {
                copy(isScanning = true)
            }
        }

        override fun onScanFinish() {
            _viewStates.setState {
                copy(isScanning = false)
            }
        }

        override fun onConnectStatusChange(connectionStatus: Connection) {
            super.onConnectStatusChange(connectionStatus)
            viewModelScope.launch {
                _viewEvents.setEvent(BleViewEvent.ConnectStatusChange(connectionStatus))
            }
        }
    }

    init {
        RiderLink.instance.addConnectCallback(riderServiceCallback)
    }

    override fun onCleared() {
        RiderLink.instance.removeConnectCallback(riderServiceCallback)
    }

    companion object {
        private const val TAG = "BleViewModel"
    }
}