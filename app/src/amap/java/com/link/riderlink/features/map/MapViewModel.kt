package com.link.riderlink.features.map

import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.link.rideramap.api.MapCallback
import com.link.rideramap.map.location.domain.entity.LocationInfo
import com.link.riderlink.RiderLink
import com.link.riderlink.utils.common.Destination
import com.link.riderlink.ui.extensions.SharedFlowEvents
import com.link.riderlink.ui.extensions.saveDestination
import com.link.riderlink.ui.extensions.setEvent
import com.link.riderlink.ui.extensions.setState
import com.link.riderservice.api.callback.RiderServiceCallback
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @date 2022/8/9
 */

class MapViewModel : ViewModel() {
    private val _viewStates = MutableStateFlow(MapViewState())

    val viewStates = _viewStates.asStateFlow()

    private val _viewEvents = SharedFlowEvents<MapViewEvent>()

    val viewEvents = _viewEvents.asSharedFlow()


    fun dispatch(viewAction: MapViewAction) {
        when (viewAction) {
            is MapViewAction.GeocodeSearch -> {
                longClick(viewAction.context, viewAction.latitude, viewAction.longitude)
            }
            is MapViewAction.OnRoute -> {
                setRoute(viewAction.context, viewAction.latitude, viewAction.longitude, viewAction.callback)
            }
            is MapViewAction.GetLocation -> {
                getLocation()
            }
            is MapViewAction.StartLocation -> {
                startLocation()
            }
        }
    }

    private fun getLocation() {
        viewModelScope.launch {
            val locationInfo = RiderLink.instance.getLbsLocation()
            Log.d(TAG, "updateLocation $locationInfo")
            _viewEvents.setEvent(MapViewEvent.UpdateLocation(locationInfo))
        }
    }

    private fun startLocation() {
        Log.d(TAG, "startLocation")
        viewModelScope.launch {
            val locationInfo = RiderLink.instance.startLbsLocation()
            _viewEvents.setEvent(MapViewEvent.UpdateLocation(locationInfo))
        }
    }

    private fun longClick(context: Context, latitude: Double, longitude: Double) {
        viewModelScope.launch {
            val locationInfo = geocodeSearch(context, latitude, longitude)
            context.saveDestination(
                Destination(
                    latitude = locationInfo?.latitude ?: 0.0,
                    longitude = locationInfo?.longitude ?: 0.0,
                    address = locationInfo?.address ?: "",
                    district = locationInfo?.district ?: ""
                )
            )
            locationInfo?.let {
                _viewEvents.setEvent(MapViewEvent.UpdateSearchLocation(it))
            }
        }
    }
    private fun setRoute(context: Context, latitude: Double, longitude: Double, callback: () -> Unit) {
        viewModelScope.launch {
            val locationInfo = geocodeSearch(context, latitude, longitude)
            context.saveDestination(
                Destination(
                    latitude = locationInfo?.latitude ?: 0.0,
                    longitude = locationInfo?.longitude ?: 0.0,
                    address = locationInfo?.address ?: "",
                    district = locationInfo?.district ?: ""
                )
            )
            callback()
        }
    }

    private val connectCallback = object : RiderServiceCallback() {
        override fun changeMap(type: Int) {
            super.changeMap(type)
            _viewStates.setState {
                copy(mapState = type)
            }
        }
    }

    private val mMapCallback = object : MapCallback() {
        override fun changeMap(type: Int) {
            super.changeMap(type)
            Log.e(TAG, "changeMap: mMapCallback", )
            _viewStates.setState {
                copy(mapState = type)
            }
        }
    }
    private suspend fun geocodeSearch(
        context: Context,
        latitude: Double,
        longitude: Double
    ): LocationInfo? = RiderLink.instance.geocodeSearch(context, latitude, longitude)
    init {
        RiderLink.instance.addConnectCallback(connectCallback)
        RiderLink.instance.addMapCallback(mMapCallback)
    }
    companion object {
        private const val TAG = "MapViewModel"
    }
}