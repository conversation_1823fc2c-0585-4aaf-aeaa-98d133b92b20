package com.link.riderlink

import android.content.Context
import android.media.projection.MediaProjectionConfig
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.view.Display
import androidx.fragment.app.FragmentActivity
import com.amap.api.maps.AMap
import com.amap.api.navi.enums.PathPlanningStrategy
import com.amap.api.navi.enums.TravelStrategy
import com.amap.api.navi.model.AMapLaneInfo
import com.amap.api.navi.model.AMapNaviCross
import com.amap.api.navi.model.AMapNaviRouteNotifyData
import com.amap.api.navi.model.NaviPoi
import com.link.rideramap.api.MapCallback
import com.link.rideramap.api.PlanRoutesListener
import com.link.rideramap.api.RiderMap
import com.link.rideramap.api.dto.NavigationInfo
import com.link.rideramap.api.dto.PoiAddress
import com.link.rideramap.api.dto.SearchResult
import com.link.rideramap.map.location.domain.entity.LocationInfo
import com.link.rideramap.map.weather.domain.entity.AMapWeatherData
import com.link.riderdvr.utils.mainScope
import com.link.riderlink.core.shared.riderlink.BaseRiderLink
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.dto.ArriveDestination
import com.link.riderservice.api.dto.GpsSignal
import com.link.riderservice.api.dto.LaneInfo
import com.link.riderservice.api.dto.NaviCross
import com.link.riderservice.api.dto.NaviInfo
import com.link.riderservice.api.dto.NaviInitSuccess
import com.link.riderservice.api.dto.NaviRoute
import com.link.riderservice.api.dto.NaviStart
import com.link.riderservice.api.dto.NaviStop
import com.link.riderservice.api.dto.NaviText
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * AMap实现的RiderLink
 * 继承自BaseRiderLink，实现AMap特有的功能
 * 
 * Created on 2023/1/11.
 * <AUTHOR>
 */
class RiderLink : BaseRiderLink() {

    //------------------------------- AMap特有的MapCallback实现 ------------------------------//

    private val mapCallback = object : MapCallback() {
        override fun onGpsSignalWeak(isWeak: Boolean) {
            super.onGpsSignalWeak(isWeak)
            RiderService.instance.sendMessageToRiderService(GpsSignal(isWeak))
        }

        override fun onGetNavigationText(type: Int, text: String) {
            super.onGetNavigationText(type, text)
            val navigationText = NaviText(type, text)
            RiderService.instance.sendMessageToRiderService(navigationText)
        }

        override fun showLaneInfo(laneInfo: AMapLaneInfo) {
            super.showLaneInfo(laneInfo)
            RiderService.instance.sendMessageToRiderService(
                LaneInfo(
                    backgroundLane = laneInfo.backgroundLane,
                    frontLane = laneInfo.frontLane,
                    laneCount = laneInfo.laneCount
                )
            )
        }

        override fun onInitNaviSuccess() {
            super.onInitNaviSuccess()
            RiderService.instance.sendMessageToRiderService(NaviInitSuccess())
        }

        override fun onArriveDestination() {
            super.onArriveDestination()
            RiderService.instance.sendMessageToRiderService(ArriveDestination())
        }

        override fun onNaviRouteNotify(mapNaviRouteNotifyData: AMapNaviRouteNotifyData) {
            super.onNaviRouteNotify(mapNaviRouteNotifyData)
            val navigationRoute = NaviRoute(
                distance = mapNaviRouteNotifyData.distance,
                latitude = mapNaviRouteNotifyData.latitude,
                longitude = mapNaviRouteNotifyData.longitude,
                reason = mapNaviRouteNotifyData.reason ?: "",
                roadName = mapNaviRouteNotifyData.roadName ?: "",
                subTitle = mapNaviRouteNotifyData.subTitle ?: "",
                isSuccess = mapNaviRouteNotifyData.isSuccess,
                notifyType = mapNaviRouteNotifyData.notifyType
            )
            RiderService.instance.sendMessageToRiderService(navigationRoute)
        }

        override fun showCross(mapNaviCross: AMapNaviCross) {
            super.showCross(mapNaviCross)
            val navigationCross = NaviCross(
                width = mapNaviCross.width,
                height = mapNaviCross.height,
                picFormat = mapNaviCross.picFormat,
                bitmap = mapNaviCross.bitmap
            )
            RiderService.instance.sendMessageToRiderService(navigationCross)
        }

        override fun onStartNavi() {
            super.onStartNavi()
            if (previousNavigationMode is com.link.riderservice.api.dto.NaviMode.MirrorNAVI) {
                return
            }
            RiderService.instance.sendMessageToRiderService(NaviStart())
        }

        override fun onStopNavi() {
            super.onStopNavi()
            if (previousNavigationMode is com.link.riderservice.api.dto.NaviMode.MirrorNAVI) {
                return
            }
            RiderService.instance.sendMessageToRiderService(NaviStop())
        }

        override fun onEndEmulatorNavi() {
            super.onEndEmulatorNavi()
            RiderService.instance.sendMessageToRiderService(ArriveDestination())
            RiderService.instance.sendMessageToRiderService(NaviStop())
        }

        override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
            val naviInfo = NaviInfo(
                curLink = navigationInfo.curLink,
                curPoint = navigationInfo.curPoint,
                curStep = navigationInfo.curStep,
                curStepRetainDistance = navigationInfo.curStepRetainDistance,
                curStepRetainTime = navigationInfo.curStepRetainTime,
                naviType = navigationInfo.naviType,
                pathRetainTime = navigationInfo.pathRetainTime,
                pathRetainDistance = navigationInfo.pathRetainDistance,
                routeRemainLightCount = navigationInfo.routeRemainLightCount,
                pathId = navigationInfo.pathId,
                nextRoadName = navigationInfo.nextRoadName,
                currentRoadName = navigationInfo.currentRoadName,
                iconType = navigationInfo.iconType,
                mapType = navigationInfo.mapType,
                turnIconName = navigationInfo.turnIconName,
                turnKind = ""
            )
            RiderService.instance.sendMessageToRiderService(naviInfo)
        }

        override fun changeMap(type: Int) {
            super.changeMap(type)
            val context = RiderService.instance.getApplication()
            if (type == AMap.MAP_TYPE_NORMAL) {
                applyThemeMode(context, nightMode = false, updateFollowSystemToFalseForDayMode = true)
            } else if (type == AMap.MAP_TYPE_NIGHT) {
                applyThemeMode(context, nightMode = true)
            }
        }
    }

    //------------------------------- AMap特有的公共方法 ------------------------------//

    suspend fun getLbsLocation() = RiderMap.instance.getLbsLocation()

    suspend fun startLbsLocation() = RiderMap.instance.startLbsLocation()

    suspend fun geocodeSearch(
        context: Context,
        latitude: Double,
        longitude: Double
    ): LocationInfo? {
        return RiderMap.instance.geocodeSearch(context, latitude, longitude)
    }

    suspend fun search(keyWord: String): SearchResult {
        return RiderMap.instance.search(keyWord)
    }

    suspend fun searchPOI(poiAddress: PoiAddress): SearchResult {
        return RiderMap.instance.searchPOI(poiAddress)
    }

    fun calculateRideRoute(
        from: NaviPoi,
        to: NaviPoi,
        planRoutesListener: PlanRoutesListener
    ) {
        RiderMap.instance.calculateRideRoute(from, to, TravelStrategy.MULTIPLE, planRoutesListener)
    }

    fun calculateDriveRoute(
        from: NaviPoi,
        to: NaviPoi,
        planRoutesListener: PlanRoutesListener
    ) {
        RiderMap.instance.calculateDriveRoute(
            from,
            to,
            PathPlanningStrategy.DRIVING_MULTIPLE_ROUTES_DEFAULT,
            planRoutesListener
        )
    }

    fun startBleScan(shouldAutoConnect: Boolean = true) {
        RiderService.instance.startBleScan(shouldAutoConnect)
    }

    suspend fun getAMapWeatherInfo(): AMapWeatherData {
        return RiderMap.instance.getWeatherInfo()
    }

    //------------------------------- 抽象方法实现 ------------------------------//

    override fun getTag(): String = TAG

    override fun addMapCallback() {
        RiderMap.instance.addCallback(mapCallback)
    }

    override fun onStopNavi() {
        RiderMap.instance.stopNavi()
    }

    override fun onStartNavi() {
        RiderMap.instance.startNavi()
    }

    override fun initNaviScreenProjection(display: Display, isSupportCircularScreen: Boolean) {
        mainScope.launch(Dispatchers.Main) {
            RiderMap.instance.initNaviScreenProjection(display, isSupportCircularScreen)
        }
    }

    override fun onRequestMediaProjectionImpl(activity: FragmentActivity) {
        val mediaProjectionManager =
            activity.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        val captureIntent =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                mediaProjectionManager.createScreenCaptureIntent(MediaProjectionConfig.createConfigForDefaultDisplay())
            } else {
                mediaProjectionManager.createScreenCaptureIntent()
            }
        activityResultLauncher?.launch(captureIntent)
    }

    override fun onChangeMapType(type: Int) {
        val context = RiderService.instance.getApplication()
        if (type == NAVI_DAYTIME) {
            applyThemeMode(context, nightMode = false)
        } else if (type == NAVI_NIGHT) {
            applyThemeMode(context, nightMode = true)
        }
    }

    override fun onDestroy() {
        RiderMap.instance.destroy()
    }

    override fun onChangeVirtualNaviMap(type: Int, enabled: Boolean) {
        RiderMap.instance.changeVirtualNaviMap(type, enabled)
    }

    override fun onSetNaviType(isSimulationMode: Boolean) {
        RiderMap.instance.setNaviType(isSimulationMode)
    }
    
    override fun onAddExternalMapCallback(mapCallback: Any) {
        if (mapCallback is MapCallback) {
            RiderMap.instance.addCallback(mapCallback)
        }
    }
    
    override fun onRemoveExternalMapCallback(mapCallback: Any) {
        if (mapCallback is MapCallback) {
            RiderMap.instance.removeCallback(mapCallback)
        }
    }

    companion object {
        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            RiderLink()
        }
        
        /**协议昼夜白天模式: 0*/
        private const val NAVI_DAYTIME = 0
        /**协议昼夜黑夜模式: 1*/
        private const val NAVI_NIGHT = 1
        private const val TAG = "AMapRiderLink"
    }
}