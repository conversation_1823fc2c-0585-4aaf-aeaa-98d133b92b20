<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:main_connect_view="http://schemas.android.com/apk/res-auto"
    xmlns:main_tool_view="http://schemas.android.com/apk/res-auto"
    android:id="@+id/home_view_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_page_background">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="9dp"
        android:src="@drawable/main_home"
        app:layout_constraintBottom_toTopOf="@+id/connect_state_view"
        app:layout_constraintLeft_toLeftOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_view"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="54dp"
        android:layout_marginRight="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/menu_more"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:background="#00000000"
            android:src="@drawable/main_menu"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.link.riderlink.ui.components.home.MainConnectView
            android:id="@+id/button_scan"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:layout_marginStart="20dp"
            app:layout_constraintStart_toEndOf="@+id/menu_more"
            app:layout_constraintTop_toTopOf="parent"
            main_connect_view:MainConnectBackground="@drawable/button_connect_blue_select"
            main_connect_view:MainConnectBitmap="@drawable/scanner"
            main_connect_view:MainConnectText="@string/home_connect_scan_text" />

        <com.link.riderlink.ui.components.home.MainConnectView
            android:id="@+id/button_connect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            main_connect_view:MainConnectBackground="@drawable/button_connect_blue_select"
            main_connect_view:MainConnectBitmap="@drawable/connect_w"
            main_connect_view:MainConnectText="@string/home_connect_button_text" />

        <com.link.riderlink.ui.components.home.MainConnectView
            android:id="@+id/button_disconnect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="0dp"
            android:visibility="gone"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            main_connect_view:MainConnectBackground="@drawable/button_connect_white_select"
            main_connect_view:MainConnectBitmap="@drawable/connect_b"
            main_connect_view:MainConnectText="@string/disconnect"
            main_connect_view:MainConnectTextColor="@color/black_tx" />

        <com.link.riderlink.ui.components.home.MainConnectView
            android:id="@+id/button_mirror"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="10dp"
            android:visibility="gone"
            app:layout_constraintRight_toLeftOf="@id/button_disconnect"
            app:layout_constraintTop_toTopOf="parent"
            main_connect_view:MainConnectBackground="@drawable/button_connect_white_select"
            main_connect_view:MainConnectBitmap="@drawable/mirror"
            main_connect_view:MainConnectText="@string/mirror"
            main_connect_view:MainConnectTextColor="@color/black_tx" />
        <TextView
            android:id="@+id/tv_weather"
            android:layout_width="wrap_content"
            android:layout_height="15dp"
            android:layout_marginTop="16dp"
            android:textSize="11sp"
            android:text="@string/weather"
            android:textColor="@color/grey_tx"
            app:layout_constraintTop_toBottomOf="@id/menu_more"
            app:layout_constraintLeft_toLeftOf="@id/top_view"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/connect_state_view"
        android:layout_width="wrap_content"
        android:layout_height="17dp"
        android:layout_marginBottom="17dp"
        app:layout_constraintBottom_toTopOf="@+id/main_tool_box"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <ImageView
            android:id="@+id/connect_state_point"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:layout_marginTop="5dp"
            android:src="@drawable/un_connect_point"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/connect_state_text"
            android:layout_width="wrap_content"
            android:layout_height="17dp"
            android:layout_marginStart="6.5dp"
            android:layout_marginTop="1dp"
            android:text="@string/connect_status"
            android:textSize="12sp"
            app:layout_constraintLeft_toRightOf="@+id/connect_state_point"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/connect_state_sign"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_marginStart="4dp"
            android:layout_marginTop="2dp"
            android:src="@drawable/sign"
            android:visibility="gone"
            app:layout_constraintLeft_toRightOf="@+id/connect_state_text"
            app:layout_constraintTop_toTopOf="@+id/connect_state_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_tool_box"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="54dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.link.riderlink.ui.components.home.MainToolView
            android:id="@+id/main_button_map"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/main_button_dvr"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            main_connect_view:MainToolText="@string/home_map_text"
            main_connect_view:MainToolTextColor="@color/black_tx"
            main_connect_view:MainToolTitle="@string/home_map_title"
            main_connect_view:MainToolTitleColor="@color/black_tx"
            main_tool_view:MainToolBitmap="@drawable/main_tool_bitmap_map1" />

        <ImageView
            android:id="@+id/main_location_bitmap"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="23dp"
            android:layout_marginEnd="61dp"
            android:src="@drawable/location_unconnect"
            app:layout_constraintRight_toRightOf="@+id/main_button_map"
            app:layout_constraintTop_toTopOf="@+id/main_button_map" />

        <TextView
            android:id="@+id/main_location_tv"
            android:layout_width="114dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:lines="2"
            android:textColor="@color/blue_tx"
            android:textSize="11sp"
            app:layout_constraintLeft_toLeftOf="@+id/main_location_bitmap"
            app:layout_constraintRight_toRightOf="@+id/main_location_bitmap"
            app:layout_constraintTop_toBottomOf="@+id/main_location_bitmap" />

        <com.link.riderlink.ui.components.home.MainToolView
            android:id="@+id/main_button_dvr"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/main_button_qq_m"
            app:layout_constraintLeft_toLeftOf="@+id/main_button_map"
            main_connect_view:MainToolTextColor="@color/black_tx"
            main_connect_view:MainToolTitleColor="@color/black_tx"
            main_tool_view:MainToolBitmap="@drawable/main_tool_bitmap_dvr1"
            main_tool_view:MainToolText="@string/home_dvr_text"
            main_tool_view:MainToolTitle="@string/home_dvr_title" />

        <com.link.riderlink.ui.components.home.MainToolView
            android:id="@+id/main_button_qq_m"
            android:layout_width="match_parent"
            android:layout_height="68dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@+id/main_button_map"
            main_connect_view:MainToolTextColor="@color/black_tx"
            main_connect_view:MainToolTitleColor="@color/black_tx"
            main_tool_view:MainToolBitmap="@drawable/main_tool_bitmap_qq_m1"
            main_tool_view:MainToolText="@string/home_music_text"
            main_tool_view:MainToolTitle="@string/home_music_title" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>