package com.link.riderlink.ui.components

import android.animation.ObjectAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.view.animation.DecelerateInterpolator
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.view.ViewConfigurationCompat
import kotlin.math.sqrt


class DragFloatActionButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {
    
    private var parentHeight = 0
    private var parentWidth = 0
    private val touchSlop: Int
    private var downX = 0f
    private var downY = 0f
    private var lastX = 0f
    private var lastY = 0f
    private var isDragging = false
    private var clickCallback: OnFloatButtonCallback? = null

    init {
        touchSlop = ViewConfigurationCompat.getScaledPagingTouchSlop(
            ViewConfiguration.get(context)
        )
    }

    interface OnFloatButtonCallback {
        fun onClick()
    }

    fun setOnFloatButtonClickListener(callback: OnFloatButtonCallback?) {
        // 清除原有的点击监听器，避免冲突
        setOnClickListener(null)
        clickCallback = callback
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        val rawX = event.rawX
        val rawY = event.rawY
        
        when (event.action and MotionEvent.ACTION_MASK) {
            MotionEvent.ACTION_DOWN -> {
                isPressed = true
                isDragging = false
                parent?.requestDisallowInterceptTouchEvent(true)
                
                // 记录按下时的坐标
                downX = rawX
                downY = rawY
                lastX = rawX
                lastY = rawY
                
                // 获取父容器尺寸
                updateParentDimensions()
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                if (parentHeight <= 0 || parentWidth <= 0) {
                    // 父容器尺寸无效，无法拖动
                    return false
                }
                
                val dx = rawX - lastX
                val dy = rawY - lastY
                
                // 计算从按下点到当前点的总距离
                val totalDistance = sqrt(
                    ((rawX - downX) * (rawX - downX) + (rawY - downY) * (rawY - downY)).toDouble()
                ).toFloat()
                
                // 判断是否开始拖拽
                if (totalDistance > touchSlop) {
                    isDragging = true
                    parent?.requestDisallowInterceptTouchEvent(true)
                    
                    // 计算新位置并限制在边界内
                    val newX = (x + dx).coerceIn(0f, (parentWidth - width).toFloat())
                    val newY = (y + dy).coerceIn(0f, (parentHeight - height).toFloat())
                    
                    x = newX
                    y = newY
                    
                    lastX = rawX
                    lastY = rawY
                }
                return isDragging
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                isPressed = false
                parent?.requestDisallowInterceptTouchEvent(false)
                
                if (isDragging) {
                    // 拖拽结束，执行吸附动画
                    performWeltAnimation(rawX)
                } else {
                    // 未拖拽，触发点击事件
                    performClick()
                }
                
                isDragging = false
                return true
            }
        }
        
        return super.onTouchEvent(event)
    }

    override fun performClick(): Boolean {
        super.performClick()
        clickCallback?.onClick()
        return true
    }

    private fun updateParentDimensions() {
        (parent as? ViewGroup)?.let { parentView ->
            parentHeight = parentView.height
            parentWidth = parentView.width
        }
    }

    private val isAtLeftEdge: Boolean
        get() = x <= 0f

    private val isAtRightEdge: Boolean
        get() = x >= (parentWidth - width).toFloat()

    private fun performWeltAnimation(currentX: Float) {
        // 如果已经在边缘，不需要动画
        if (isAtLeftEdge || isAtRightEdge) {
            return
        }
        
        val targetX = if (currentX >= parentWidth / 2) {
            // 靠右吸附
            (parentWidth - width).toFloat()
        } else {
            // 靠左吸附
            0f
        }
        
        ObjectAnimator.ofFloat(this, "x", x, targetX).apply {
            interpolator = DecelerateInterpolator()
            duration = 300 // 缩短动画时间，提升响应性
            start()
        }
    }
}