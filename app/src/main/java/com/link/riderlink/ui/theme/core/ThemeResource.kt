package com.link.riderlink.ui.theme.core

import android.content.Context
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat

/**
 * 类型安全的主题资源封装
 */
sealed class ThemeResource<T> {
    
    /**
     * 颜色资源对
     */
    data class ColorPair(
        @ColorRes val dayColorRes: Int,
        @ColorRes val nightColorRes: Int
    ) : ThemeResource<Int>() {
        
        fun getCurrentColor(context: Context, themeProvider: ThemeProvider): Int {
            val colorRes = if (themeProvider.isNightMode()) nightColorRes else dayColorRes
            return ContextCompat.getColor(context, colorRes)
        }
        
        fun getCurrentColorRes(themeProvider: ThemeProvider): Int {
            return if (themeProvider.isNightMode()) nightColorRes else dayColorRes
        }
    }
    
    /**
     * Drawable资源对
     */
    data class DrawablePair(
        @DrawableRes val dayDrawableRes: Int,
        @DrawableRes val nightDrawableRes: Int? = null
    ) : ThemeResource<Int>() {
        
        fun getCurrentDrawableRes(themeProvider: ThemeProvider): Int {
            return if (themeProvider.isNightMode() && nightDrawableRes != null) {
                nightDrawableRes
            } else {
                dayDrawableRes
            }
        }
    }
}

/**
 * 主题资源常量定义
 */
object ThemeResources {
    
    // ===================== 颜色资源 =====================
    
    // 基础颜色
    val PRIMARY_BACKGROUND = ThemeResource.ColorPair(
        com.link.riderlink.R.color.theme_white,
        com.link.riderlink.R.color.theme_background_night
    )
    
    val PRIMARY_TEXT = ThemeResource.ColorPair(
        com.link.riderlink.R.color.theme_primary_dark,
        com.link.riderlink.R.color.theme_white
    )
    
    val SECONDARY_TEXT = ThemeResource.ColorPair(
        com.link.riderlink.R.color.theme_secondary_text,
        com.link.riderlink.R.color.theme_white
    )
    
    // 对话框颜色
    val DIALOG_BACKGROUND = ThemeResource.ColorPair(
        com.link.riderlink.R.color.theme_white,
        com.link.riderlink.R.color.theme_dialog_night_bg
    )
    
    val DIALOG_TEXT = ThemeResource.ColorPair(
        com.link.riderlink.R.color.theme_black,
        com.link.riderlink.R.color.theme_white
    )
    
    // 状态颜色
    val STATUS_SUCCESS = ThemeResource.ColorPair(
        com.link.riderlink.R.color.theme_primary_dark,
        com.link.riderlink.R.color.theme_success
    )
    
    val STATUS_ERROR = ThemeResource.ColorPair(
        com.link.riderlink.R.color.grey_tx,
        com.link.riderlink.R.color.theme_error
    )
    
    // ===================== Drawable资源 =====================
    
    val BACK_ICON = ThemeResource.DrawablePair(com.link.riderlink.R.drawable.set_back)
    val ROUTE_ICON = ThemeResource.DrawablePair(com.link.riderlink.R.drawable.item_route)
    val MOON_ICON = ThemeResource.DrawablePair(com.link.riderlink.R.drawable.item_moon)
    val HELP_ICON = ThemeResource.DrawablePair(com.link.riderlink.R.drawable.circle_help)
    val EDIT_ICON = ThemeResource.DrawablePair(com.link.riderlink.R.drawable.edit_alt)
    val INFO_ICON = ThemeResource.DrawablePair(com.link.riderlink.R.drawable.circle_information)
    val AUDIO_ICON = ThemeResource.DrawablePair(com.link.riderlink.R.drawable.bluetooth_audio)
    
    // 背景资源
    val ITEM_BACKGROUND_D = ThemeResource.DrawablePair(com.link.riderlink.R.drawable.item_background_d)
    val ITEM_BACKGROUND_S = ThemeResource.DrawablePair(com.link.riderlink.R.drawable.item_background_s)
    val SEARCH_BACKGROUND = ThemeResource.DrawablePair(com.link.riderlink.R.drawable.search_background)
    val CONNECT_DIALOG_BACKGROUND = ThemeResource.DrawablePair(com.link.riderlink.R.drawable.connect_dialog_background)
    
    // 对话框按钮资源
    val DELETE_BTN_CANCEL = ThemeResource.DrawablePair(com.link.riderlink.R.drawable.delect_btn_cancel)
    val DELETE_BTN_OK = ThemeResource.DrawablePair(com.link.riderlink.R.drawable.delect_btn_ok)
    
    // 批量资源集合
    val SETTING_PAGE_ICONS = listOf(MOON_ICON, HELP_ICON, EDIT_ICON, INFO_ICON, AUDIO_ICON)
    val SETTING_PAGE_BACKGROUNDS = listOf(ITEM_BACKGROUND_D, ITEM_BACKGROUND_D, ITEM_BACKGROUND_S, ITEM_BACKGROUND_S)
} 