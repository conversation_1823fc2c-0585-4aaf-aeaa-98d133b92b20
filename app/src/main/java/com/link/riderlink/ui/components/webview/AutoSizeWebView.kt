package com.link.riderlink.ui.components.webview

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.webkit.WebView
import me.jessyan.autosize.AutoSize

/**
 * <AUTHOR>
 * @date 2022/10/20
 */

class AutoSizeWebView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyle: Int = 0
) : WebView(context, attrs, defStyle) {
    override fun setOverScrollMode(mode: Int) {
        super.setOverScrollMode(mode)
        AutoSize.autoConvertDensityOfGlobal(context as Activity)
    }
}