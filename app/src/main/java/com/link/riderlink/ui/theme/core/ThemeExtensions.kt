package com.link.riderlink.ui.theme.core

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.link.riderlink.ui.extensions.popBackStack

// 全局主题提供者实例
val Fragment.themeProvider: ThemeProvider
    get() = DefaultThemeProvider.getInstance(requireContext())

val View.themeProvider: ThemeProvider
    get() = DefaultThemeProvider.getInstance(context)

// ===================== 基础扩展函数 =====================

/**
 * TextView设置主题文字颜色
 */
fun TextView.setThemeTextColor(colorPair: ThemeResource.ColorPair, themeProvider: ThemeProvider = this.themeProvider) {
    setTextColor(colorPair.getCurrentColor(context, themeProvider))
}

/**
 * View设置主题背景颜色
 */
fun View.setThemeBackgroundColor(colorPair: ThemeResource.ColorPair, themeProvider: ThemeProvider = this.themeProvider) {
    setBackgroundColor(colorPair.getCurrentColor(context, themeProvider))
}

/**
 * ImageView设置主题图标
 */
fun ImageView.setThemeImageResource(drawablePair: ThemeResource.DrawablePair, themeProvider: ThemeProvider = this.themeProvider) {
    setImageResource(drawablePair.getCurrentDrawableRes(themeProvider))
}

/**
 * View设置主题背景资源
 */
fun View.setThemeBackgroundResource(drawablePair: ThemeResource.DrawablePair, themeProvider: ThemeProvider = this.themeProvider) {
    setBackgroundResource(drawablePair.getCurrentDrawableRes(themeProvider))
}

/**
 * 兼容老版本API的扩展函数
 */
fun ImageView.setThemeImageResource(@DrawableRes resId: Int, themeProvider: ThemeProvider = this.themeProvider) {
    setImageResource(themeProvider.getDrawableResource(resId))
}

fun View.setThemeBackgroundResource(@DrawableRes resId: Int, themeProvider: ThemeProvider = this.themeProvider) {
    setBackgroundResource(themeProvider.getDrawableResource(resId))
}

// ===================== 批量操作扩展函数 =====================

/**
 * 批量设置TextView文字颜色
 */
fun setThemeTextColor(colorPair: ThemeResource.ColorPair, vararg textViews: TextView) {
    val themeProvider = textViews.firstOrNull()?.themeProvider ?: return
    textViews.forEach { it.setThemeTextColor(colorPair, themeProvider) }
}

/**
 * 批量设置View背景颜色
 */
fun setThemeBackgroundColor(colorPair: ThemeResource.ColorPair, vararg views: View) {
    val themeProvider = views.firstOrNull()?.themeProvider ?: return
    views.forEach { it.setThemeBackgroundColor(colorPair, themeProvider) }
}

// ===================== DSL主题设置 =====================

/**
 * 主题设置作用域
 */
class ThemeScope(private val themeProvider: ThemeProvider) {
    
    fun TextView.textColor(colorPair: ThemeResource.ColorPair) {
        setThemeTextColor(colorPair, themeProvider)
    }
    
    fun View.backgroundColor(colorPair: ThemeResource.ColorPair) {
        setThemeBackgroundColor(colorPair, themeProvider)
    }
    
    fun ImageView.icon(drawablePair: ThemeResource.DrawablePair) {
        setThemeImageResource(drawablePair, themeProvider)
    }
    
    fun View.backgroundResource(drawablePair: ThemeResource.DrawablePair) {
        setThemeBackgroundResource(drawablePair, themeProvider)
    }
    
    // 兼容老版本的方法
    fun ImageView.icon(@DrawableRes resId: Int) {
        setThemeImageResource(resId, themeProvider)
    }
    
    fun View.backgroundResource(@DrawableRes resId: Int) {
        setThemeBackgroundResource(resId, themeProvider)
    }
    
    // 批量操作
    fun textColor(colorPair: ThemeResource.ColorPair, vararg textViews: TextView) {
        textViews.forEach { it.setThemeTextColor(colorPair, themeProvider) }
    }
    
    fun backgroundColor(colorPair: ThemeResource.ColorPair, vararg views: View) {
        views.forEach { it.setThemeBackgroundColor(colorPair, themeProvider) }
    }
    
    fun icons(drawablePairs: List<ThemeResource.DrawablePair>, vararg imageViews: ImageView) {
        drawablePairs.zip(imageViews) { drawablePair, imageView ->
            imageView.setThemeImageResource(drawablePair, themeProvider)
        }
    }
}

/**
 * 应用主题的DSL函数
 */
inline fun View.applyTheme(crossinline block: ThemeScope.() -> Unit) {
    ThemeScope(themeProvider).block()
}

inline fun Fragment.applyTheme(crossinline block: ThemeScope.() -> Unit) {
    ThemeScope(themeProvider).block()
}

// ===================== Fragment扩展函数 =====================

/**
 * Fragment快速设置返回按钮
 */
fun Fragment.setupBackButton(
    imageView: ImageView,
    onClick: () -> Unit = { popBackStack() }
) {
    imageView.setThemeImageResource(ThemeResources.BACK_ICON)
    imageView.setOnClickListener { onClick() }
}

/**
 * Fragment观察主题变化
 */
fun Fragment.observeTheme(
    onThemeChanged: (ThemeMode) -> Unit
) {
    val listener = object : ThemeChangeListener {
        override fun onThemeChanged(newMode: ThemeMode) {
            onThemeChanged(newMode)
        }
    }
    
    themeProvider.registerThemeChangeListener(listener)
    
    // 自动在Fragment销毁时取消监听
    viewLifecycleOwner.lifecycle.addObserver(object : DefaultLifecycleObserver {
        override fun onDestroy(owner: LifecycleOwner) {
            themeProvider.unregisterThemeChangeListener(listener)
        }
    })
}

// ===================== 快速主题设置器 =====================

/**
 * 通用主题助手对象
 */
object ThemeHelper {
    
    /**
     * 设置标准页面主题
     */
    fun setStandardPageTheme(
        rootView: View,
        titleView: TextView
    ) {
        rootView.setThemeBackgroundColor(ThemeResources.PRIMARY_BACKGROUND)
        titleView.setThemeTextColor(ThemeResources.PRIMARY_TEXT)
    }
    
    /**
     * 设置版本页面主题
     */
    fun setVersionPageTheme(
        rootView: View,
        titleView: TextView,
        contentView: TextView
    ) {
        rootView.setThemeBackgroundColor(ThemeResources.PRIMARY_BACKGROUND)
        setThemeTextColor(ThemeResources.PRIMARY_TEXT, titleView, contentView)
    }
    
    /**
     * 设置设置页面主题
     */
    fun setSettingPageTheme(
        rootView: View,
        titleView: TextView,
        settingTextViews: List<TextView>,
        settingIcons: List<ImageView>,
        settingBackgrounds: List<View>
    ) {
        rootView.setThemeBackgroundColor(ThemeResources.PRIMARY_BACKGROUND)
        titleView.setThemeTextColor(ThemeResources.PRIMARY_TEXT)
        
        // 批量设置文字颜色
        settingTextViews.forEach { it.setThemeTextColor(ThemeResources.PRIMARY_TEXT) }
        
        // 批量设置图标
        ThemeResources.SETTING_PAGE_ICONS.zip(settingIcons) { iconRes, imageView ->
            imageView.setThemeImageResource(iconRes)
        }
        
        // 批量设置背景
        ThemeResources.SETTING_PAGE_BACKGROUNDS.zip(settingBackgrounds) { bgRes, view ->
            view.setThemeBackgroundResource(bgRes)
        }
    }
    
    /**
     * 设置对话框主题
     */
    fun setDialogTheme(
        dialogBackground: View,
        titleView: TextView,
        contentView: TextView,
        cancelButton: View? = null,
        okButton: View? = null
    ) {
        dialogBackground.setThemeBackgroundResource(ThemeResources.CONNECT_DIALOG_BACKGROUND)
        titleView.setThemeTextColor(ThemeResources.DIALOG_TEXT)
        contentView.setThemeTextColor(ThemeResources.DIALOG_TEXT)
        
        cancelButton?.setThemeBackgroundResource(ThemeResources.DELETE_BTN_CANCEL)
        okButton?.setThemeBackgroundResource(ThemeResources.DELETE_BTN_OK)
    }
    
    /**
     * 设置设置页面所有文字颜色（兼容老API）
     */
    fun setSettingPageTextTheme(vararg textViews: TextView) {
        setThemeTextColor(ThemeResources.PRIMARY_TEXT, *textViews)
    }
} 