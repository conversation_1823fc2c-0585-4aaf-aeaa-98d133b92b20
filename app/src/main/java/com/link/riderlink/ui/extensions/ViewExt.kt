package com.link.riderlink.ui.extensions

import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.EditText
import androidx.annotation.ColorInt
import androidx.core.view.WindowCompat
import androidx.fragment.app.Fragment
import com.link.riderlink.ui.theme.ThemeManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @date 2022/6/29
 */

inline fun View.debounceClick(windowDuration: Long = 500, crossinline block: () -> Unit) {
    var job: Job? = null
    this.setOnClickListener {
        job?.cancel()
        job = CoroutineScope(Dispatchers.Main).launch {
            delay(windowDuration)
            block()
        }
    }
}

fun EditText.textChangeFlow(): Flow<Editable> {
    return callbackFlow {
        val watcher = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                s?.let {
                    trySend(it)
                }
            }

            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {

            }

            override fun onTextChanged(s: CharSequence?, p1: Int, p2: Int, p3: Int) {

            }
        }
        addTextChangedListener(watcher)
        awaitClose { removeTextChangedListener(watcher) }
    }.conflate()
}

fun Fragment.changeSystemBarColorActually(@ColorInt newStatusBarColor: Int,@ColorInt newNavigationBarColor: Int ) {
    val window = requireActivity().window
    window.statusBarColor = newStatusBarColor
    window.navigationBarColor = newNavigationBarColor
}

fun Fragment.changeAppearanceStatusBars(isNavi: Boolean){
    val window = requireActivity().window
    val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
    // true 表示深色图标，false 表示浅色图标
    if (isNavi) {
        windowInsetsController.isAppearanceLightStatusBars = false
    } else {
        windowInsetsController.isAppearanceLightStatusBars = !ThemeManager.isNightMode(requireActivity())
    }
}
