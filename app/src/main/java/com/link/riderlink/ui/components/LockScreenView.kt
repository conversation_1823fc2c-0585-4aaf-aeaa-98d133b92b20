package com.link.riderlink.ui.components

import android.content.Context
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.util.Log
import android.view.MotionEvent
import android.view.WindowManager
import android.widget.Scroller
import androidx.constraintlayout.widget.ConstraintLayout
import kotlin.math.abs


class LockScreenView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val mScroller: Scroller = Scroller(context)
    private val mScreenHeight // 窗口高度
            : Int
    private var mLastY = 0
    private var mStart = 0
    private var mEnd = 0
    private var mCallback: onLSCallback? = null

    init {
        // 获取屏幕高度
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val metrics = DisplayMetrics()
        windowManager.defaultDisplay.getRealMetrics(metrics)
        Log.e(
            TAG,
            "ScrollViewGroup: heightPixels ${metrics.heightPixels}"
        )
        mScreenHeight = metrics.heightPixels
    }


    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                mLastY = event.y.toInt()
                mStart = scrollY
                // 请求父控件不要拦截事件
                parent?.requestDisallowInterceptTouchEvent(true)
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                if (!mScroller.isFinished) {
                    // 终止滑动动画，防止多次滑动冲突
                    mScroller.abortAnimation()
                }
                var offsetY = (mLastY - event.y).toInt()
                // 到达顶部，防止越界
                if (scrollY + offsetY < 0) {
                    offsetY = -scrollY
                }
                // 到达底部，防止越界
                if (scrollY + offsetY > mScreenHeight) {
                    offsetY = mScreenHeight - scrollY
                }
                scrollBy(0, offsetY)
                mLastY = event.y.toInt()
                return true
            }

            MotionEvent.ACTION_UP -> {
                mEnd = scrollY
                val distance = mEnd - mStart
                if (distance < mScreenHeight / 3) {
                    // 回弹到原位置
                    mScroller.startScroll(0, scrollY, 0, -distance)
                } else {
                    // 滚动到屏幕底部
                    mScroller.startScroll(0, scrollY, 0, mScreenHeight - distance)
                }
                postInvalidate()
                // 事件消费
                return true
            }
        }
        return super.onTouchEvent(event)
    }

    override fun computeScroll() {
        if (mScroller.computeScrollOffset()) {
            scrollTo(mScroller.currX, mScroller.currY)
            postInvalidate()
            // 容差判断，防止因精度问题导致回调不触发
            if (abs(mScroller.currY - mScreenHeight) < 5) {
                mCallback?.onLockUp()
                // 自动复位
                mScroller.startScroll(0, scrollY, 0, -mScreenHeight)
            }
        }
    }

    interface onLSCallback {
        fun onLockUp()
    }

    fun setOnLSCallback(l: onLSCallback) {
        mCallback = l
    }

    companion object {
        private const val TAG = "ScrollView"
    }
}