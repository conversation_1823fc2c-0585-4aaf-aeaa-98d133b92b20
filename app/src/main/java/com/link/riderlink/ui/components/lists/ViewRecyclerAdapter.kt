package com.link.riderlink.ui.components.lists

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView

/**
 * <AUTHOR>
 * @date 2022/6/29
 */
class ViewRecyclerAdapter(private val mViewBinders: MutableList<ViewBinder<*>> = mutableListOf()) :
    RecyclerView.Adapter<RecyclerViewHolder>() {

    fun add(aVar: ViewBinder<*>) {
        mViewBinders.add(aVar)
        notifyItemInserted(mViewBinders.size - 1)
    }

    override fun onBindViewHolder(recyclerViewHolder: RecyclerViewHolder, i: Int) {
        mViewBinders[i].bindView(recyclerViewHolder)
    }

    override fun onCreateViewHolder(viewGroup: ViewGroup, i: Int): RecyclerViewHolder {
        return RecyclerViewHolder.Companion.onCreateView(viewGroup, i)
    }

    fun clear() {
        val size = mViewBinders.size
        if (size > 0) {
            mViewBinders.clear()
            notifyItemRangeRemoved(0, size)
        }
    }

    override fun getItemCount(): Int {
        return mViewBinders.size
    }

    override fun getItemViewType(index: Int): Int {
        return mViewBinders[index].binderLayout
    }
}