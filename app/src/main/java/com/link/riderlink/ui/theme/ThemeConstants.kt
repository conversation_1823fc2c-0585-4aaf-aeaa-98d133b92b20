package com.link.riderlink.ui.theme

import com.link.riderlink.ui.theme.core.ThemeResources

/**
 * 向后兼容的主题常量
 * 提供旧版本API的兼容性
 */
@Deprecated("Use ThemeResources instead", ReplaceWith("ThemeResources"))
object ThemeColors {
    
    @Deprecated("Use ThemeResources.PRIMARY_BACKGROUND", ReplaceWith("ThemeResources.PRIMARY_BACKGROUND"))
    val PRIMARY_BACKGROUND = ThemeResources.PRIMARY_BACKGROUND
    
    @Deprecated("Use ThemeResources.PRIMARY_TEXT", ReplaceWith("ThemeResources.PRIMARY_TEXT"))
    val PRIMARY_TEXT = ThemeResources.PRIMARY_TEXT
    
    @Deprecated("Use ThemeResources.SECONDARY_TEXT", ReplaceWith("ThemeResources.SECONDARY_TEXT"))
    val SECONDARY_TEXT = ThemeResources.SECONDARY_TEXT
    
    @Deprecated("Use ThemeResources.STATUS_SUCCESS", ReplaceWith("ThemeResources.STATUS_SUCCESS"))
    val STATUS_SUCCESS = ThemeResources.STATUS_SUCCESS
    
    @Deprecated("Use ThemeResources.STATUS_ERROR", ReplaceWith("ThemeResources.STATUS_ERROR"))
    val STATUS_ERROR = ThemeResources.STATUS_ERROR
    
    @Deprecated("Use ThemeResources.DIALOG_BACKGROUND", ReplaceWith("ThemeResources.DIALOG_BACKGROUND"))
    val DIALOG_BACKGROUND = ThemeResources.DIALOG_BACKGROUND
}

@Deprecated("Use ThemeResources instead", ReplaceWith("ThemeResources"))
object ThemeIcons {
    
    @Deprecated("Use ThemeResources.BACK_ICON", ReplaceWith("ThemeResources.BACK_ICON"))
    val BACK_ICON = ThemeResources.BACK_ICON
    
    @Deprecated("Use ThemeResources.ITEM_BACKGROUND_S", ReplaceWith("ThemeResources.ITEM_BACKGROUND_S"))
    val ITEM_BACKGROUND_S = ThemeResources.ITEM_BACKGROUND_S
    
    @Deprecated("Use ThemeResources.ITEM_BACKGROUND_D", ReplaceWith("ThemeResources.ITEM_BACKGROUND_D"))
    val ITEM_BACKGROUND_D = ThemeResources.ITEM_BACKGROUND_D
} 