package com.link.riderlink.ui.theme

import android.content.Context
import androidx.core.content.ContextCompat
import com.link.riderlink.R

/**
 * 主题颜色配置对象
 * 用于管理所有日间/夜间模式的颜色对，避免硬编码重复
 * 重构后使用 colors.xml 中的颜色资源
 */
object ThemeColors {

    // 颜色对的数据类，支持资源ID和颜色字符串两种方式
    data class ColorPair(val dayColorRes: Int, val nightColorRes: Int) {
        fun getCurrentColorInt(context: Context): Int {
            val colorRes = if (ThemeManager.isNightMode(context)) nightColorRes else dayColorRes
            return ContextCompat.getColor(context, colorRes)
        }
    }

    // ===================== 基础颜色常量 =====================

    // 主要背景色：白色/深蓝灰
    val PRIMARY_BACKGROUND = ColorPair(R.color.theme_white, R.color.theme_background_night)

    // 版本页面背景（与主要背景相同）
    val VERSION_BACKGROUND = PRIMARY_BACKGROUND

    // ===================== 文字颜色常量 =====================

    // 主要文字颜色：深灰/白色
    val PRIMARY_TEXT = ColorPair(R.color.theme_primary_dark, R.color.theme_white)

    // 版本页面文字（与主要文字相同）
    val VERSION_TEXT = PRIMARY_TEXT

    // 搜索相关文字（与主要文字相同）
    val SEARCH_TEXT = PRIMARY_TEXT

    // 适配器文字颜色：深灰/浅灰
    val ADAPTER_TEXT = ColorPair(R.color.theme_primary_dark, R.color.theme_adapter_night_text)

    // 次要文字颜色：浅灰/白色
    val SECONDARY_TEXT = ColorPair(R.color.theme_secondary_text, R.color.theme_white)

    // ===================== 对话框颜色常量 =====================

    // 对话框背景：白色/深灰
    val DIALOG_BACKGROUND = ColorPair(R.color.theme_white, R.color.theme_dialog_night_bg)

    // 对话框文字：黑色/白色
    val DIALOG_TEXT = ColorPair(R.color.theme_black, R.color.theme_white)

    // ===================== 状态颜色常量 =====================

    // 状态颜色 - 连接成功：深灰/绿色
    val STATUS_SUCCESS = ColorPair(R.color.theme_primary_dark, R.color.theme_success)

    // 状态颜色 - 连接错误：中灰/红色
    val STATUS_ERROR = ColorPair(R.color.grey_tx, R.color.theme_error)

    // ===================== 辅助方法 =====================

    /**
     * 获取当前主题下的颜色值
     */
    fun getColorInt(context: Context, colorPair: ColorPair): Int {
        return colorPair.getCurrentColorInt(context)
    }

    /**
     * 批量获取颜色值
     */
    fun getColorsInt(context: Context, vararg colorPairs: ColorPair): List<Int> {
        return colorPairs.map { it.getCurrentColorInt(context) }
    }
} 