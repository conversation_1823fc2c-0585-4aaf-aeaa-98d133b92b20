package com.link.riderlink.ui.components.lists

import androidx.annotation.LayoutRes

/**
 * <AUTHOR>
 * @date 2022/6/29
 */
abstract class ViewBinder<Any>(private val mData: Any) {
    fun bindView(recyclerViewHolder: RecyclerViewHolder) {
        onCreateView(recyclerViewHolder, mData)
    }

    @get:LayoutRes
    abstract val binderLayout: Int

    abstract fun onCreateView(recyclerViewHolder: RecyclerViewHolder, t: Any)



    override fun hashCode(): Int {
        return mData.hashCode()
    }

    override fun equals(other: kotlin.Any?): Bo<PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ViewBinder<*>

        if (mData != other.mData) return false

        return true
    }

}