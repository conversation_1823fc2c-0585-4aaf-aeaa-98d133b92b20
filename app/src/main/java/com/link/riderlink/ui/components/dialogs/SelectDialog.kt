package com.link.riderlink.ui.components.dialogs

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.link.riderlink.R
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.theme.ThemeManager

class SelectDialog private constructor(context: Context, theme: Int = 0) : Dialog(context, theme) {

    private var isDialogShowing = false

    class Builder(private val context: Context) {
        private var title: String? = null
        private var message: String? = null
        private var cancelText: String? = null
        private var confirmText: String? = null
        private var cancelClickListener: (() -> Unit)? = null
        private var confirmClickListener: (() -> Unit)? = null

        // UI 组件
        private var confirmButton: TextView? = null
        private var cancelButton: TextView? = null
        private var titleView: TextView? = null
        private var messageView: TextView? = null
        private var selectView: LinearLayout? = null

        private val layout: View
        private val dialog: SelectDialog = SelectDialog(context)

        // 主题变化监听器
        private val themeChangeListener = object : ThemeManager.OnThemeChangeListener() {
            override fun onThemeChanged() {
                applyTheme()
            }
        }

        init {
            val inflater = LayoutInflater.from(context)
            layout = inflater.inflate(R.layout.dialog_select_layout, null)
            dialog.setContentView(layout)

            initViews()
            setupDialog()
            applyTheme()
        }

        private fun initViews() {
            confirmButton = layout.findViewById(R.id.select_ok)
            cancelButton = layout.findViewById(R.id.select_cancel)
            titleView = layout.findViewById(R.id.ds_title)
            messageView = layout.findViewById(R.id.ds_text)
            selectView = layout.findViewById(R.id.select_view)
        }

        private fun setupDialog() {
            dialog.setCancelable(true)
            dialog.setCanceledOnTouchOutside(false)
            dialog.setOnCancelListener {
                dialog.isDialogShowing = false
                ThemeManager.unregisterThemeChangeListener(themeChangeListener)
            }
            dialog.setOnDismissListener {
                dialog.isDialogShowing = false
                ThemeManager.unregisterThemeChangeListener(themeChangeListener)
            }
        }

        fun setTitle(title: String): Builder {
            this.title = title
            return this
        }

        fun setMessage(message: String): Builder {
            this.message = message
            return this
        }

        fun setCancelText(cancelText: String): Builder {
            this.cancelText = cancelText
            return this
        }

        fun setOnCancelClick(listener: () -> Unit): Builder {
            this.cancelClickListener = listener
            return this
        }

        fun setConfirmText(confirmText: String): Builder {
            this.confirmText = confirmText
            return this
        }

        fun setOnConfirmClick(listener: () -> Unit): Builder {
            this.confirmClickListener = listener
            return this
        }

        fun setCancelable(cancelable: Boolean): Builder {
            dialog.setCancelable(cancelable)
            return this
        }

        fun setCanceledOnTouchOutside(cancelable: Boolean): Builder {
            dialog.setCanceledOnTouchOutside(cancelable)
            return this
        }

        fun setCancelButtonVisibility(visibility: Int): Builder {
            cancelButton?.visibility = visibility
            return this
        }

        fun setConfirmButtonVisibility(visibility: Int): Builder {
            confirmButton?.visibility = visibility
            return this
        }

        fun setSelectViewVisibility(visibility: Int): Builder {
            selectView?.visibility = visibility
            return this
        }

        fun isShowing(): Boolean {
            return dialog.isDialogShowing && dialog.isShowing
        }

        fun getTitle(): String? = title

        fun getMessage(): String? = message

        fun create(): SelectDialog {
            // 设置文本内容
            title?.let { titleView?.text = it }
            message?.let { messageView?.text = it }
            cancelText?.let { cancelButton?.text = it }
            confirmText?.let { confirmButton?.text = it }

            // 设置点击监听器
            confirmButton?.setOnClickListener {
                confirmClickListener?.invoke()
                dismiss()
            }

            cancelButton?.setOnClickListener {
                cancelClickListener?.invoke()
                dismiss()
            }

            return dialog
        }

        fun show(): SelectDialog {
            val createdDialog = create()
            ThemeManager.registerThemeChangeListener(themeChangeListener)
            createdDialog.show()
            return createdDialog
        }

        fun dismiss() {
            ThemeManager.unregisterThemeChangeListener(themeChangeListener)
            dialog.dismiss()
        }

        private fun applyTheme() {
            // 使用主题颜色常量，大大简化代码
            val textColor = ThemeColors.DIALOG_TEXT.getCurrentColorInt(context)
            val backgroundColor = ThemeColors.DIALOG_BACKGROUND.getCurrentColorInt(context)

            confirmButton?.apply {
                setTextColor(textColor)
                setBackgroundColor(backgroundColor)
            }
            cancelButton?.apply {
                setTextColor(textColor)
                setBackgroundColor(backgroundColor)
            }
            titleView?.apply {
                setTextColor(textColor)
                setBackgroundColor(backgroundColor)
            }
            messageView?.apply {
                setTextColor(textColor)
                setBackgroundColor(backgroundColor)
            }
        }
    }

    override fun show() {
        super.show()
        isDialogShowing = true
    }

    override fun dismiss() {
        super.dismiss()
        isDialogShowing = false
    }
}