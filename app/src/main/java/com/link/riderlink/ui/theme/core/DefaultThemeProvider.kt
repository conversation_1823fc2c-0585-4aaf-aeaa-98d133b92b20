package com.link.riderlink.ui.theme.core

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.util.Log
import android.util.LruCache
import androidx.annotation.DrawableRes
import java.util.concurrent.CopyOnWriteArraySet

/**
 * 默认主题提供者实现
 * 提供高性能的资源缓存和主题管理
 */
class DefaultThemeProvider(private val context: Context) : ThemeProvider {
    
    companion object {
        private const val TAG = "DefaultThemeProvider"
        private const val RESOURCE_SUFFIX = "_night"
        private const val CACHE_SIZE = 200
        
        // 全局单例实例
        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var INSTANCE: DefaultThemeProvider? = null
        
        fun getInstance(context: Context): DefaultThemeProvider {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DefaultThemeProvider(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    // 当前主题模式
    private var currentThemeMode = ThemeMode.DAY
    
    // 主题变化监听器集合（线程安全）
    private val themeChangeListeners = CopyOnWriteArraySet<ThemeChangeListener>()
    
    // LRU缓存，提升资源查找性能
    private val resourceCache = LruCache<String, Int>(CACHE_SIZE)
    
    override fun isNightMode(): Boolean {
        return currentThemeMode == ThemeMode.NIGHT
    }
    
    override fun getColorResource(dayColorRes: Int, nightColorRes: Int): Int {
        return if (isNightMode()) nightColorRes else dayColorRes
    }
    
    override fun getDrawableResource(@DrawableRes dayDrawableRes: Int): Int {
        if (currentThemeMode == ThemeMode.DAY) {
            return dayDrawableRes
        }
        
        // 尝试从缓存获取
        val cacheKey = "${dayDrawableRes}_night"
        resourceCache.get(cacheKey)?.let { return it }
        
        // 缓存未命中，查找夜间资源
        val nightResId = findNightResource(dayDrawableRes)
        
        // 缓存结果
        resourceCache.put(cacheKey, nightResId)
        
        return nightResId
    }
    
    /**
     * 查找夜间模式资源
     * 优化的资源查找逻辑，减少异常处理
     */
    private fun findNightResource(@DrawableRes dayResId: Int): Int {
        return try {
            val entryName = context.resources.getResourceEntryName(dayResId)
            val typeName = context.resources.getResourceTypeName(dayResId)
            
            val nightResId = context.resources.getIdentifier(
                "$entryName$RESOURCE_SUFFIX",
                typeName,
                context.packageName
            )
            
            if (nightResId != 0) nightResId else dayResId
            
        } catch (e: Resources.NotFoundException) {
            Log.w(TAG, "Night resource not found for $dayResId, using day resource", e)
            dayResId
        }
    }
    
    override fun registerThemeChangeListener(listener: ThemeChangeListener) {
        themeChangeListeners.add(listener)
    }
    
    override fun unregisterThemeChangeListener(listener: ThemeChangeListener) {
        themeChangeListeners.remove(listener)
    }
    
    override fun setThemeMode(mode: ThemeMode) {
        if (currentThemeMode != mode) {
            val oldMode = currentThemeMode
            currentThemeMode = mode
            
            // 清空缓存，确保使用新主题的资源
            resourceCache.evictAll()
            
            // 通知所有监听器
            notifyThemeChanged(mode)
            
            Log.d(TAG, "Theme mode changed from $oldMode to $mode")
        }
    }
    
    override fun getCurrentThemeMode(): ThemeMode {
        return currentThemeMode
    }
    
    /**
     * 通知主题变化
     */
    private fun notifyThemeChanged(newMode: ThemeMode) {
        themeChangeListeners.forEach { listener ->
            try {
                listener.onThemeChanged(newMode)
            } catch (e: Exception) {
                Log.e(TAG, "Error notifying theme change listener", e)
            }
        }
    }
    
    /**
     * 根据系统配置更新主题
     */
    fun updateThemeFromConfiguration(isNightModeActive: Boolean) {
        val themePreferences = context.getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
        val isNightModeEnabled = themePreferences?.getBoolean("mode_night", false) == true
        val isFollowSystemEnabled = themePreferences?.getBoolean("follow_system", false) == true
        
        when {
            isNightModeEnabled && isFollowSystemEnabled -> {
                setThemeMode(if (isNightModeActive) ThemeMode.NIGHT else ThemeMode.DAY)
            }
            isNightModeEnabled -> {
                setThemeMode(ThemeMode.NIGHT)
            }
            else -> {
                setThemeMode(ThemeMode.DAY)
            }
        }
    }
    
    /**
     * 判断系统是否为夜间模式
     */
    fun isSystemNightMode(): Boolean {
        return try {
            val uiMode = context.resources.configuration.uiMode
            val nightModeMask = Configuration.UI_MODE_NIGHT_MASK
            val currentNightModeSetting = uiMode.and(nightModeMask)
            currentNightModeSetting == Configuration.UI_MODE_NIGHT_YES
        } catch (e: Exception) {
            Log.w(TAG, "Error checking system night mode", e)
            false
        }
    }
    
    /**
     * 显示/隐藏状态栏
     */
    fun showBar(show: Boolean, barColor: Int) {
        themeChangeListeners.forEach { listener ->
            try {
                listener.onThemeBarChanged(show, barColor)
            } catch (e: Exception) {
                Log.e(TAG, "Error notifying theme bar change", e)
            }
        }
    }
} 