package com.link.riderlink.ui.theme.core

/**
 * 主题提供者接口
 * 提供统一的主题资源获取和状态管理
 */
interface ThemeProvider {
    /**
     * 判断当前是否为夜间模式
     */
    fun isNightMode(): Boolean
    
    /**
     * 获取主题相关的颜色资源ID
     */
    fun getColorResource(dayColorRes: Int, nightColorRes: Int): Int
    
    /**
     * 获取主题相关的drawable资源ID
     */
    fun getDrawableResource(dayDrawableRes: Int): Int
    
    /**
     * 注册主题变化监听器
     */
    fun registerThemeChangeListener(listener: ThemeChangeListener)
    
    /**
     * 取消注册主题变化监听器
     */
    fun unregisterThemeChangeListener(listener: ThemeChangeListener)
    
    /**
     * 设置主题模式
     */
    fun setThemeMode(mode: ThemeMode)
    
    /**
     * 获取当前主题模式
     */
    fun getCurrentThemeMode(): ThemeMode
}

/**
 * 主题模式枚举
 */
enum class ThemeMode {
    DAY, NIGHT
}

/**
 * 主题变化监听器
 */
interface ThemeChangeListener {
    fun onThemeChanged(newMode: ThemeMode)
    fun onThemeBarChanged(show: Boolean, barColor: Int) {}
} 