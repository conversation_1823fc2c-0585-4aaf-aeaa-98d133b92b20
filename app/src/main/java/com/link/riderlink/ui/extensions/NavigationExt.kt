package com.link.riderlink.ui.extensions

import android.os.Bundle
import androidx.annotation.IdRes
import androidx.fragment.app.Fragment
import androidx.navigation.findNavController
import com.link.riderlink.R


fun Fragment.navigate(@IdRes resId: Int) {
    requireActivity().findNavController(R.id.nav_host_fragment).navigate(resId)
}

fun Fragment.navigate(@IdRes resId: Int, bundle: Bundle) {
    requireActivity().findNavController(R.id.nav_host_fragment).navigate(resId, bundle)
}

fun Fragment.popBackStack() {
    requireActivity().findNavController(R.id.nav_host_fragment).popBackStack()
}