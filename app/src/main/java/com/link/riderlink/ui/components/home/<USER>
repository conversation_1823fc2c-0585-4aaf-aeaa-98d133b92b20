package com.link.riderlink.ui.components.home

import android.annotation.SuppressLint
import android.content.Context
import com.link.riderlink.R
import com.link.riderlink.RiderLink
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.dto.BleStatus
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.api.dto.WifiStatus

/**
 * <AUTHOR>
 * @date 2022/8/25
 */

@SuppressLint("StaticFieldLeak")
object ConnectInfoMange{
    var idletext = getIdleText()

    private const val TAG = "ConnectInfoManager"

    fun changeIdleText(){
        idletext = getIdleText()
    }


    fun updateConnectState(context: Context?,connection: Connection): String{
        val bleStatus = connection.btStatus
        val wifiStatus = connection.wifiStatus
        when (bleStatus) {
            is BleStatus.IDLE -> {
                return idletext
            }

            is BleStatus.DeviceFailedToConnect -> {
                return context?.getString(R.string.connect_failed) ?: ""
            }

            is BleStatus.DeviceConnected -> {
                var textContent = context?.getString(R.string.wifi_connecting) ?: ""
                if (wifiStatus is WifiStatus.DeviceConnected) {
                    val name = RiderLink.instance.getCurrentConnectDevice()?.completeLocalName()
                    textContent = buildString {
                        append(context?.getString(R.string.connected_append) ?: "")
                        append(name)
                    }
                }
                return textContent
            }

            is BleStatus.DeviceConnecting -> {
                return context?.getString(R.string.connecting) ?: ""
            }

            is BleStatus.DeviceDisconnected -> {
                val textContent: String = if (wifiStatus is WifiStatus.DeviceConnected) {
                    context?.getString(R.string.connect_ble_failed) ?: ""
                } else {
                    context?.getString(R.string.connect_ble_wifi_failed) ?: ""
                }
                return textContent
            }
        }
    }

    fun getIdleText() : String{
        val sharedPref = RiderService.instance.getApplication().getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
        val bleName = sharedPref.getString("ble_name","")
        val text: String = if (bleName == "") {
            RiderService.instance.getApplication().getString(R.string.connect_idle_state)
        }else{
            RiderService.instance.getApplication().getString(R.string.connect_idle_state_with_name,bleName)
        }
        return text
    }

}
