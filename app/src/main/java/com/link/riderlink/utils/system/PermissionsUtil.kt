package com.link.riderlink.utils.system

import android.content.Context
import android.view.View
import com.hjq.permissions.Permission
import com.link.riderlink.ui.components.dialogs.SelectDialog
import com.link.riderlink.ui.extensions.IS_BLUETOOTH_DENIED
import com.link.riderlink.ui.extensions.IS_LOCATION_DENIED
import com.link.riderlink.ui.extensions.getBoolean

val connectPermissions = arrayOf(
    Permission.BLUETOOTH_CONNECT,
    Permission.BLUETOOTH_SCAN,
    Permission.BLUETOOTH_ADVERTISE,
    Permission.NEARBY_WIFI_DEVICES
)
val locationPermissions = arrayOf(
    Permission.ACCESS_COARSE_LOCATION,
    Permission.ACCESS_FINE_LOCATION
)

object PermissionsUtil {

    fun showRequestPermissionDialog(context: Context, listener: () -> Unit) {
        val dialog = SelectDialog.Builder(context)
        val isBluetoothDenied = context.getBoolean(IS_BLUETOOTH_DENIED)
        val isLocationDenied = context.getBoolean(IS_LOCATION_DENIED)
        if (isBluetoothDenied || isLocationDenied) {
            val title = if (isBluetoothDenied && isLocationDenied) {
                "位置权限和附近设备连接权限受限"
            } else if (isBluetoothDenied) {
                "附近设备连接权限受限"
            } else {
                "位置权限受限"
            }
            val message = if (isBluetoothDenied && isLocationDenied) {
                "你拒绝了位置权限和附近设备连接权限，为了查找你的爱车，我们需要使用你的位置权限和附近设备连接权限，请手动设置权限"
            } else if (isBluetoothDenied) {
                "你拒绝了附近设备连接权限，为了连接你的爱车，我们需要使用你附近设备连接权限，请手动设置权限"
            } else {
                "你拒绝了位置权限，为了查找你的爱车，我们需要使用你的位置权限，请手动设置权限"
            }
            dialog.setTitle(title)
                .setMessage(message)
                .setConfirmText("知道了")
                .setCancelButtonVisibility(View.GONE)
                .show()
            return
        } else {
            dialog.setTitle("睿连智行权限使用")
                .setMessage("为了查找你的爱车并快速连接到你的爱车，我们需要使用你的位置和附近设备连接权限")
                .setConfirmText("知道了")
                .setCancelButtonVisibility(View.GONE)
                .setOnConfirmClick {
                    listener.invoke()
                }.show()
        }
    }
}