package com.link.riderlink.utils

import androidx.core.graphics.toColorInt

/**
 * 主题感知接口
 * 为Fragment提供统一的主题管理模式，减少重复的主题设置代码
 */
interface ThemeAware {
    
    /**
     * 初始化主题
     * 每个实现类只需要实现这个方法来设置自己的主题
     */
    fun initTheme()
    
    /**
     * 主题变化回调
     * 提供默认实现，直接调用initTheme()
     */
    fun onThemeChanged() {
        initTheme()
    }
    
    /**
     * 注册主题监听器
     */
    fun registerThemeListener() {
        val listener = object : ThemeManager.OnThemeChangeListener() {
            override fun onThemeChanged() {
                <EMAIL>()
            }
        }
        ThemeManager.registerThemeChangeListener(listener)
        // 注意：需要在适当的时候调用unregister，可以在各个Fragment的onDestroyView中处理
    }
}

/**
 * 主题管理基类混入
 * 提供常用的主题设置方法
 */
interface CommonThemeOperations {
    
    /**
     * 获取主题资源
     */
    fun getThemeResource(context: android.content.Context, dayResId: Int): Int {
        return ThemeManager.getCurrentThemeRes(context, dayResId)
    }
    
    /**
     * 获取主题颜色字符串
     */
    fun getThemeColor(dayColor: String, nightColor: String): String {
        return ThemeManager.autoChangeStr(dayColor, nightColor)
    }
    
    /**
     * 获取主题颜色Int值
     */
    fun getThemeColorInt(dayColor: String, nightColor: String): Int {
        return getThemeColor(dayColor, nightColor).toColorInt()
    }
} 