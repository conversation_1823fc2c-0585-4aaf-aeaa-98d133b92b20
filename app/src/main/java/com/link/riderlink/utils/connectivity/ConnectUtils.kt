package com.link.riderlink.utils.connectivity

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkInfo


/**
 * 判断当前是否有网络连接,但是如果该连接的网络无法上网，也会返回true
 * @return
 */
fun isNetConnection(context: Context?): <PERSON><PERSON>an {
    val connectivityManager =
        context?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    val networkInfo = connectivityManager.getActiveNetworkInfo()
    val connected = networkInfo?.isConnected()
    if (networkInfo != null && connected == true) {
        return networkInfo.getState() == NetworkInfo.State.CONNECTED
    }
    return false
}