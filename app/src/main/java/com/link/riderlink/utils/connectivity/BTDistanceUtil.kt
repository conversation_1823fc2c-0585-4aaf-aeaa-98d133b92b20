package com.link.riderlink.utils.connectivity

import kotlin.math.abs
import kotlin.math.pow

class BTDistanceUtil {
    companion object {
        const val RSSI_1M: Double = 50.0//发射端和接收端相隔1米时的信号强度
        const val N_VALUE: Double = 10.0//环境衰减因子

        /**
         * 根据rssi值估算出距离
         * @param rssi 信号强度
         * @return 距离(单位：米)
         */
        fun getDistance(rssi: Int): Double {
            val rssiAbs = abs(rssi)
            val power = (rssiAbs - RSSI_1M) / (10 * N_VALUE)
            return 10.0.pow(power)
        }
    }
}