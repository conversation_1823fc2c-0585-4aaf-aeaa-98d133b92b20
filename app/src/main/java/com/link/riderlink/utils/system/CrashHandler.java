package com.link.riderlink.utils.system;

import android.content.Context;
import android.os.Environment;
import android.util.Log;

import androidx.annotation.NonNull;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018/11/21
 */
public class <PERSON>Hand<PERSON> implements Thread.UncaughtExceptionHandler {
    private static CrashHandler sInstance;
    private static final String TAG = "CrashHandler";
    private static Context mContext = null;
    private Thread.UncaughtExceptionHandler mDefaultCrashHandler;

    public static CrashHandler getInstance() {
        if (sInstance == null) {
            synchronized (CrashHandler.class) {
                if (sInstance == null) {
                    sInstance = new CrashHandler();
                }
            }
        }
        return sInstance;
    }

    public void init(Context context) {
        mContext = context;
        mDefaultCrashHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(this);
    }

    @Override
    public void uncaughtException(@NonNull Thread t, @NonNull Throwable e) {
        String message = obtainExceptionInfo(e);
        Log.e(TAG, "uncaughtException:" + message);
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String filename = "crash-"+simpleDateFormat2.format(new Date())+".log";
        String PATH_LOGCAT;
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            PATH_LOGCAT = mContext.getExternalFilesDir(null).getAbsolutePath();
        } else {
            PATH_LOGCAT = mContext.getFilesDir().getAbsolutePath();
        }
        try {
            FileOutputStream fos = new FileOutputStream(PATH_LOGCAT + File.separator + filename);
            fos.write(message.getBytes());
            fos.close();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        try {
            Thread.sleep(1000);
        } catch (InterruptedException ex) {
            ex.printStackTrace();
        }
        if (mDefaultCrashHandler != null) {
            mDefaultCrashHandler.uncaughtException(t, e);
        } else {
            android.os.Process.killProcess(android.os.Process.myPid());
        }
    }

    private static String obtainExceptionInfo(Throwable throwable) {
        StackTraceElement[] stackTraceElements = throwable.getStackTrace();
        StringBuilder sb = new StringBuilder();
        for (StackTraceElement stackTraceElement : stackTraceElements) {
            sb.append(stackTraceElement.toString()).append(";\n");
        }
        Throwable cause = throwable.getCause();
        if (cause != null) {
            StackTraceElement[] causeStackTrace = cause.getStackTrace();
            for (StackTraceElement stackTraceElement : causeStackTrace) {
                sb.append(stackTraceElement.toString()).append(":");
            }
        }
        return sb.toString();
    }
}
