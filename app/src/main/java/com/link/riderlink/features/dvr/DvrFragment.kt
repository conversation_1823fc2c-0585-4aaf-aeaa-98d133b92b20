package com.link.riderlink.features.dvr

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.pm.PackageManager
import android.graphics.Color
import android.graphics.drawable.AnimationDrawable
import android.net.wifi.WifiManager
import android.net.wifi.p2p.WifiP2pManager
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.Settings
import android.util.Log
import android.view.*
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.PopupMenu
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.annotation.RequiresApi
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.SimpleItemAnimator
import com.link.riderdvr.R
import com.link.riderdvr.database.model.DownLoadTask
import com.link.riderdvr.utils.mainScope
import com.link.riderdvr.widget.IVideoView
import com.link.riderdvr.widget.MyMediaController
import com.link.riderdvr.widget.WaitDialog
import com.link.riderlink.databinding.FragmentDvrBinding
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.extensions.changeSystemBarColorActually
import com.link.riderlink.ui.extensions.debounceClick
import com.link.riderlink.ui.extensions.observeState
import com.link.riderlink.ui.components.dialogs.SelectDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import androidx.core.net.toUri
import androidx.core.graphics.drawable.toDrawable
import androidx.core.graphics.toColorInt
import com.link.riderlink.ui.theme.setThemeBackgroundColor
import com.link.riderlink.ui.theme.setThemeTextColor


/**
 * <AUTHOR>
 * @date 2024/1/15
 */

class DvrFragment : Fragment() {
    //    var context:Context?=null
    private val wifiP2pManager: WifiP2pManager? by lazy(LazyThreadSafetyMode.NONE) {
        requireContext().getSystemService(Context.WIFI_P2P_SERVICE) as WifiP2pManager?
    }

    private val wifiManager: WifiManager? by lazy(LazyThreadSafetyMode.NONE) {
        requireContext().getSystemService(Context.WIFI_SERVICE) as WifiManager?
    }
    private val mDvrViewModel: DvrViewModel by viewModels()
    private var _binding: FragmentDvrBinding? = null
    private val binding get() = _binding!!
    private var waitDialog: WaitDialog? = null
    private var controllerFull: View? = null
    private var controllerSingle: View? = null
    private var controllerNow: View? = null
    val click = { currentDvrViewState: DvrItem, _: Int, position: Int ->
        if (initP2p()) {
            mDvrViewModel.dispatch(DvrViewAction.DvrItemClicked(currentDvrViewState, position))
            mDvrViewModel.dispatch(DvrViewAction.PlayClicked(false))
            controllerNow?.findViewById<ImageView>(R.id.idle_icon)
                ?.setImageResource(R.drawable.loading_cycling)
            val loadingIcon: ImageView? = controllerNow?.findViewById(R.id.idle_icon)
            val loadingAnimation = loadingIcon?.drawable as AnimationDrawable
            loadingAnimation.start()
            mControllerViewManager?.onItemClick()
            val bitmap = adapter.bitmapCache["" + currentDvrViewState.databaseID + currentDvrViewState.title]
            if (mDvrViewModel.viewStates.value.modeStatus is ModeStatus.Online) {
                mMediaController?.setVideoURI(
                    currentDvrViewState.url.toUri(),
                    requireContext(),
                    bitmap!!.width,
                    bitmap.height
                )
            } else {
                mMediaController?.setDataPath(
                    currentDvrViewState.url,
                    requireContext(),
                    bitmap!!.width,
                    bitmap.height
                )
            }
            mMediaController?.cleaningScreen()
        } else {
            SelectDialog
                .Builder(requireContext())
                .setTitle(getString(com.link.riderlink.R.string.delete_config_title))
                .setMessage(getString(com.link.riderlink.R.string.dvr_no_support_with_wifi_direct))
                .setCancelButtonVisibility(View.GONE)
                .setConfirmButtonVisibility(View.GONE)
                .create()
                .show()
        }
    }
    private val clickStartPause = { currentDvrViewState: DvrItem, position: Int ->
        mDvrViewModel.dispatch(DvrViewAction.DownloadTaskClick(currentDvrViewState, position))//状态变更传入数据库
    }
    private val delete = { currentDvrViewState: DvrItem, position: Int ->
        Log.e(TAG, "88" + currentDvrViewState.url)
        if (!currentDvrViewState.isDownloading) {
            val deleteConfirmationDialog = showDelectDialog(object : DelectDialogListener {
                override fun onCancel() {
                    TODO("Not yet implemented")
                }

                override fun onOk() {
                    mDvrViewModel.deleteSingleFile(currentDvrViewState.path, currentDvrViewState)//删除本地文件
                    mDvrViewModel.dispatch(DvrViewAction.RequestedLocal)//删除后更新列表
                    adapter.notifyItemChanged(position)
                }

            })
            deleteConfirmationDialog.show()
        } else {
            val deleteConfirmationDialog = showDelectDialog(object : DelectDialogListener {
                override fun onCancel() {
                }

                override fun onOk() {
                    if (currentDvrViewState.downloadStatus == "downloading")
                        mDvrViewModel.dispatch(
                            DvrViewAction.DownloadTaskClick(
                                currentDvrViewState,
                                mDvrViewModel.get_downloading_posion_task()
                            )
                        )//如果是正在下载的任务，先暂停
                    if (100 != currentDvrViewState.progress) {
//                    LocalManager.instance.deleteSingleFile(it.path)//如果没有下载完成，删除本地文件
                        mDvrViewModel.deleteSingleFile(
                            mDvrViewModel.get_filepath(currentDvrViewState.url),
                            currentDvrViewState
                        )//如果没有下载完成，删除本地文件
                        adapter.notifyItemChanged(position)
                    }
                    mDvrViewModel.dispatch_database(
                        DataBaseAction.DeleteTask(
                            DownLoadTask(
                                id = mDvrViewModel.get_database_id(
                                    currentDvrViewState.url
                                )
                            )
                        )
                    )//删除任务
                }

            })
            deleteConfirmationDialog.show()
        }
    }
    val progress = {
        mDvrViewModel.viewStates.value.currentDownloadTask
    }
    val speed = {
        mDvrViewModel.viewStates.value.speed
    }
    val download = { currentDvrViewState: DvrItem ->
        if (mDvrViewModel.viewStates.value.modeStatus == ModeStatus.Online) {
            requestFile()
            mDvrViewModel.dispatch(DvrViewAction.DownloadClicked(currentDvrViewState))
        }
    }
    private val adapter: DvrAdapter by lazy {
        DvrAdapter(click, clickStartPause, delete, progress, speed, download)
    }

    private fun showWaitDialog(message: String) {
        waitDialog = WaitDialog(context)
        waitDialog?.setText(message)
        waitDialog?.show()
    }

    fun dismissWaitDialog() {
        waitDialog?.dismiss()
    }

    private var mMediaController: MyMediaController? = null
    private var mControllerViewManager: ControllerViewManager? = null
    var videoView: IVideoView? = null

    override fun onCreateView(
        layoutInflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentDvrBinding.inflate(layoutInflater, container, false)
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { view, insets ->
            view.updatePadding(
                top = insets.systemWindowInsetTop,
                bottom = insets.systemWindowInsetBottom
            )
            insets
        }
        changeSystemBarColor(
            statusBarNightColor = resources.getColor(
                com.link.riderlink.R.color.route_status_bar_night,
                null
            ),
            navigationBarNightColor = resources.getColor(
                com.link.riderlink.R.color.route_navigation_bar_night,
                null
            )
        )
        return binding.root
    }

    private fun changeSystemBarColor(
        @ColorInt statusBarColor: Int = Color.WHITE,
        @ColorInt navigationBarColor: Int = Color.WHITE,
        @ColorInt statusBarNightColor: Int,
        @ColorInt navigationBarNightColor: Int
    ) {
        val isDay = ThemeManager.themeMode == ThemeManager.ThemeMode.DAY
        val currentStatusBarColor = if (isDay) statusBarColor else statusBarNightColor
        val currentNavigationBarColor = if (isDay) navigationBarColor else navigationBarNightColor
        changeSystemBarColorActually(currentStatusBarColor, currentNavigationBarColor)
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
        mDvrViewModel.setLocalPath(requireContext().filesDir.toString())
        controllerFull = LayoutInflater.from(context).inflate(R.layout.video_controller_full, null)
        controllerSingle = LayoutInflater.from(context).inflate(R.layout.video_controller, null)
        binding.onlineDvr.let { onlineDvrButton ->
            onlineDvrButton.isSelected = true
            onlineDvrButton.setText(getString(com.link.riderlink.R.string.all_dvr))
            onlineDvrButton.setOnClickListener {
                binding.locateDvr.isSelected = false
                it.isSelected = true
                mDvrViewModel.dispatch(DvrViewAction.ModeChangeClicked(ModeStatus.Online))
            }
        }
        binding.locateDvr.let {
            it.isSelected = false
            it.setText(getString(com.link.riderlink.R.string.dvr_downloaded))
            it.setOnClickListener {
                binding.onlineDvr.isSelected = false
                it.isSelected = true
                mDvrViewModel.dispatch(DvrViewAction.ModeChangeClicked(ModeStatus.Local))
            }
        }
        mDvrViewModel.setListener(object : DvrViewModel.DvrListener {
            override fun progressChanged(position: Int) {
//                adapter.submitList(mDvrViewModel.mapViewState.value.downloadedTasks)
                mainScope.launch(Dispatchers.Main) {
                    adapter.notifyItemChanged(position)
                }
            }

            override fun selectedChanged(position: Int) {
                mainScope.launch(Dispatchers.Main) {
                    Log.e(TAG, "selectedChanged: $position")
                    adapter.notifyItemChanged(position)
                }
            }

        })
        mDvrViewModel.viewStates.let { dvrViewStates ->
            dvrViewStates.observeState(this, DvrViewState::isFullscreen) {
                controllerNow = if (it) {
                    controllerFull
                } else {
                    controllerSingle
                }
                if (mDvrViewModel.viewStates.value.isPlaying) {
                    controllerNow?.findViewById<ImageView>(R.id.play)
                        ?.setImageResource(R.drawable.pause)
                    controllerNow?.findViewById<ImageView>(R.id.play1)
                        ?.setImageResource(R.drawable.pause)
                } else {
                    controllerNow?.findViewById<ImageView>(R.id.play)
                        ?.setImageResource(R.drawable.play)
                    controllerNow?.findViewById<ImageView>(R.id.play1)
                        ?.setImageResource(R.drawable.play)
                }
                if (mDvrViewModel.viewStates.value.isVolumeOn) {
                    controllerNow?.findViewById<ImageView>(R.id.btn_volume)
                        ?.setImageResource(R.drawable.volume_on_selector)
                } else {
                    controllerNow?.findViewById<ImageView>(R.id.btn_volume)
                        ?.setImageResource(R.drawable.volume_off_selector)
                }
                onScreenChange(it)
            }
            dvrViewStates.observeState(this, DvrViewState::isVolumeOn) {
                if (it) {
                    controllerNow?.findViewById<ImageView>(R.id.btn_volume)?.setImageResource(
                        R.drawable.volume_on_selector
                    )
                } else {
                    controllerNow?.findViewById<ImageView>(R.id.btn_volume)?.setImageResource(
                        R.drawable.volume_off_selector
                    )
                }
//                Log.e("62",it.toString())
                mMediaController?.setVolumeState(it)
            }
            dvrViewStates.observeState(this, DvrViewState::isPlaying) {
                Log.e(
                    TAG,
                    "146" + "mDvrViewModel.mapViewState.value.isPlaying=" + mDvrViewModel.viewStates.value.isPlaying
                )
                if (it) {
                    controllerNow?.findViewById<ImageView>(R.id.play)
                        ?.setImageResource(R.drawable.pause)
                    controllerNow?.findViewById<ImageView>(R.id.play1)
                        ?.setImageResource(R.drawable.pause)
                    mMediaController?.reStart()
                    Log.e("139", it.toString())
                } else {
                    controllerNow?.findViewById<ImageView>(R.id.play)
                        ?.setImageResource(R.drawable.play)
                    controllerNow?.findViewById<ImageView>(R.id.play1)
                        ?.setImageResource(R.drawable.play)
                    mMediaController?.pause()
                    Log.e("134", it.toString())
                }
//                mMediaController?.show()
            }
            dvrViewStates.observeState(this, DvrViewState::isPlaybackComplete) {
                if (it && mDvrViewModel.viewStates.value.isPlaying)
                    mDvrViewModel.dispatch(DvrViewAction.PlayClicked(false))
            }
//            states.observeState(this, DvrViewState::isBrightnessControlVisible){
//                if (it){
//                    controllerRoot_now?.findViewById<ImageView>(R.id.brightness)?.setImageResource(
//                        R.drawable.brightness_selector)
//                    mControllerViewManager!!.brightness_volume_show(View.VISIBLE)
//                }else{
//                    controllerRoot_now?.findViewById<ImageView>(R.id.brightness)?.setImageResource(
//                        R.drawable.brightness_selector)
//                    mControllerViewManager!!.brightness_volume_show(View.GONE)
//                }
//            }
            dvrViewStates.observeState(this, DvrViewState::isPlayerPrepared) {
                if (it) {
                    mMediaController!!.setVolumeState(mDvrViewModel.viewStates.value.isVolumeOn)
                    mControllerViewManager!!.onPrepared()
                }
            }
            dvrViewStates.observeState(this, DvrViewState::isOnlineListRequested) {
                if (!it) {
                    showWaitDialog(getString(com.link.riderlink.R.string.dvr_loading))
                } else {
                    dismissWaitDialog()
                }
            }
            dvrViewStates.observeState(this, DvrViewState::downloadStatus) {
                if (it == DownloadStatus.IDLE) {
                    mDvrViewModel.find_task_in_list()
                }
            }
            dvrViewStates.observeState(this, DvrViewState::downloadProgress) {//下载进度更新
//                Log.e(TAG+244,DownloadStatus.Downloading.toString())
                if (mDvrViewModel.viewStates.value.downloadStatus == DownloadStatus.Downloading) {
//                    LocalManager.instance.updateNotification(it)
                    if (mDvrViewModel.viewStates.value.modeStatus == ModeStatus.Online) {
                        adapter.notifyItemChanged(mDvrViewModel.get_downloading_posion_online())//刷新单个正在下载中的item
                    }
                }
            }
            dvrViewStates.observeState(this, DvrViewState::downloadTasks) {//数据库成员列表更新
//                if (mDvrViewModel.mapViewState.value.modeStatus == ModeStatus.Task){
//                    mDvrViewModel.dispatch(DvrViewAction.RequestedTask)
//                }
                if (mDvrViewModel.viewStates.value.downloadStatus == DownloadStatus.IDLE) {
                    mDvrViewModel.find_task_in_list()
                }
            }
            dvrViewStates.observeState(this, DvrViewState::onlineList) {//在线列表更新
                Log.e(TAG, "onViewCreated: DvrViewModel onlineList1")
                if (mDvrViewModel.viewStates.value.modeStatus == ModeStatus.Online) {
                    adapter.submitList(it)
                    Log.e(TAG, "onViewCreated: DvrViewModel onlineList2")
                }
            }
            dvrViewStates.observeState(this, DvrViewState::localList) {//本地列表更新
                if (mDvrViewModel.viewStates.value.modeStatus == ModeStatus.Local) {
                    val completedLocalDvrItems: MutableList<DvrItem> = ArrayList()
                    it.forEach { localDvrItem ->
                        if (!localDvrItem.isDownloading) {
                            Log.e(TAG, "onViewCreated: $localDvrItem")
                            completedLocalDvrItems.add(localDvrItem)
                        }
                    }
                    adapter.submitList(completedLocalDvrItems)
                }
            }
            dvrViewStates.observeState(this, DvrViewState::downloadedTasks) {//任务列表更新
                if (mDvrViewModel.viewStates.value.modeStatus == ModeStatus.Task) {
                    adapter.submitList(it)
                    for (index in it.indices) {
                        Log.e("287:$TAG$index", "" + it[index])
                    }
                    it.forEach { taskDvrItem ->
                        Log.e("290", "" + taskDvrItem)
                    }
                    Log.e(TAG, "295:downloadedTasks")
                }
            }
            dvrViewStates.observeState(this, DvrViewState::modeStatus) {//任务列表更新
                when (it) {
                    is ModeStatus.Online -> {
                        adapter.submitList(mDvrViewModel.viewStates.value.onlineList)
                    }

                    is ModeStatus.Local -> {
                        val completedLocalDvrItems: MutableList<DvrItem> = ArrayList()
                        mDvrViewModel.viewStates.value.localList.forEach { localDvrItem ->
                            if (!localDvrItem.isDownloading) {
                                Log.e(TAG, "onViewCreated: $localDvrItem")
                                completedLocalDvrItems.add(localDvrItem)
                            }
                        }
                        adapter.submitList(completedLocalDvrItems)
                    }

                    is ModeStatus.Task -> {
                        adapter.submitList(mDvrViewModel.viewStates.value.downloadedTasks)
                    }
                }
            }
        }
        controllerNow = controllerSingle
        mDvrViewModel.dispatch(DvrViewAction.RequestedOnline)
//        binding
        (binding.dvrList.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false//取消闪烁
//        binding.dvrList.addItemDecoration(DividerItemDecoration(context, DividerItemDecoration.VERTICAL))
        binding.llBack.debounceClick {
            requireActivity().findNavController(com.link.riderlink.R.id.nav_host_fragment)
                .popBackStack()
        }
        binding.dvrList.layoutManager = LinearLayoutManager(activity)
        binding.dvrList.adapter = adapter

        videoView = binding.ivideoview
        mMediaController = MyMediaController(requireContext())
        mMediaController?.setListener(object : MyMediaController.OnVideoListener {
            override fun onPause(isPaused: Boolean) {
                mControllerViewManager?.show()
            }

            override fun show(durationInSeconds: Int) {
                mControllerViewManager?.show(durationInSeconds)
            }

            override fun showVerticalBar(progress: Int, verticalBarMode: String) {
                mControllerViewManager?.brightness_volume_show(View.VISIBLE, progress, verticalBarMode)
            }

            override fun showVideoBar(progress: Float) {
                mControllerViewManager?.media_show(progress)
            }

            override fun touchOn() {
                mControllerViewManager?.touchOn()
            }

            override fun show() {
                mControllerViewManager?.show()
            }

            override fun hide() {
                mControllerViewManager?.hide()
            }

            override fun hideVerticalBar() {
                mControllerViewManager?.brightness_volume_show(View.GONE, 0, "")
            }

            override fun onCompleted() {
                mDvrViewModel.dispatch(DvrViewAction.Completed)
            }

            override fun onPrepared() {
                mDvrViewModel.dispatch(DvrViewAction.OnPrepared)
            }
        })
        mControllerViewManager = ControllerViewManager(requireContext())
        mControllerViewManager?.setListener(object : ControllerViewManager.ControllerViewListener {
            override fun onStopTrackingTouch(progress: Int) {
                mMediaController!!.seekTo(progress)
                mDvrViewModel.dispatch(DvrViewAction.PlayClicked(false))
            }

            override fun getCurrentPosition(): Int {
                return mMediaController!!.getCurrentPosition()
            }

            override fun getDuration(): Int {
                return mMediaController!!.getDuration()
            }

            override fun showOnIVideo(shouldShowOnIVideo: Boolean) {
                try {
//                    if (b) {
//                        if (mDvrViewModel.mapViewState.value.isFullscreen)
//                            mMediaController?.setVideoforeground(ContextCompat.getDrawable(context!!, R.drawable.full_gradient))
//                        else
//                            mMediaController?.setVideoforeground(ContextCompat.getDrawable(context!!, R.drawable.single_gradient))
//                    } else {
//                        if (mDvrViewModel.mapViewState.value.isFullscreen)
//                            mMediaController?.setVideoforeground(ContextCompat.getDrawable(context!!, R.drawable.full_ordinarily))
//                        else
//                            mMediaController?.setVideoforeground(ContextCompat.getDrawable(context!!, R.drawable.single_ordinarily))
//                    }
                } catch (exception: NullPointerException) {
                    exception.message?.let { Log.e(TAG, it) }
                }
            }

        })
        addControllView()
        mMediaController!!.attach(videoView!!)
        videoView!!.setMediaController(mMediaController!!)
        setExtTool()
    }

    private fun setExtTool() {
        binding.menuView.setOnClickListener {
            showPopupMenu(binding.menuView)
        }
    }

    private fun requestFile() {
        Log.e(TAG, Build.VERSION.SDK_INT.toString() + ">=" + Build.VERSION_CODES.M.toString())
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {// android 11  且 不是已经被拒绝
            // 先判断有没有权限
            if (!Environment.isExternalStorageManager()) {
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                intent.data = ("package:" + requireContext().packageName).toUri();
                startActivity(intent);
            }
        }
    }

    private fun showPopupMenu(view: View) {
        // View当前PopupMenu显示的相对View的位置
        val popupMenu = PopupMenu(context, view)
        // menu布局
        popupMenu.menuInflater.inflate(R.menu.toolbar_menu, popupMenu.getMenu())

        popupMenu.setOnMenuItemClickListener {
            if (it.itemId == R.id.onlinedvr) {
                mDvrViewModel.dispatch(DvrViewAction.ModeChangeClicked(ModeStatus.Online))
            }

            if (it.itemId == R.id.localdvr) {
                mDvrViewModel.dispatch(DvrViewAction.ModeChangeClicked(ModeStatus.Local))
            }
            if (it.itemId == R.id.downloadtask) {
                mDvrViewModel.dispatch(DvrViewAction.ModeChangeClicked(ModeStatus.Task))
            }
            if (it.itemId == R.id.refresh) {
                mDvrViewModel.dispatch(DvrViewAction.RefreshList)
            }
            return@setOnMenuItemClickListener true
        }
        popupMenu.show()
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun addControllView() {
        mMediaController?.setcontrollerRoot(controllerNow)
        mMediaController?.attach(videoView!!)
        mControllerViewManager?.let {
            controllerNow?.findViewById<ImageView>(R.id.play)?.setOnClickListener {
                if (mDvrViewModel.viewStates.value.isPlayerPrepared) {
                    mDvrViewModel.dispatch(DvrViewAction.PlayClicked(!mDvrViewModel.viewStates.value.isPlaying))
                }
            }
            controllerNow?.findViewById<ImageView>(R.id.play1)?.setOnClickListener {
                if (mDvrViewModel.viewStates.value.isPlayerPrepared) {
                    mDvrViewModel.dispatch(DvrViewAction.PlayClicked(!mDvrViewModel.viewStates.value.isPlaying))
                }
            }

            controllerNow?.findViewById<ImageView>(R.id.fastforward)?.setOnClickListener {
                if (mMediaController!!.canSeekForward() && mDvrViewModel.viewStates.value.isPlayerPrepared) {
                    mMediaController?.seekTo(mMediaController!!.getCurrentPosition() + 16000)
                } else if (mDvrViewModel.viewStates.value.isPlayerPrepared) {
                    mMediaController?.seekTo(mMediaController!!.getDuration())
                }
                mMediaController?.show()
            }
            controllerNow?.findViewById<ImageView>(R.id.rewind)?.setOnClickListener {
                if (mMediaController!!.canSeekBackward() && mDvrViewModel.viewStates.value.isPlayerPrepared) {
                    mMediaController?.seekTo(mMediaController!!.getCurrentPosition() - 16000)
                } else if (mDvrViewModel.viewStates.value.isPlayerPrepared) {
                    mMediaController?.seekTo(0)
                }
                mMediaController?.show()
            }
            controllerNow?.findViewById<ImageView>(R.id.brightness)?.setOnClickListener {
                mDvrViewModel.dispatch(DvrViewAction.BrightnessClicked)
            }
            it.setSeekBar_mediat(controllerNow?.findViewById(R.id.seek_bar))
            it.setSeekBar_brightness(controllerNow?.findViewById(R.id.seekBar_brightness))
            it.setSeekBar_volume(controllerNow?.findViewById(R.id.seekBar_volume))
            it.setButton_brightness(controllerNow?.findViewById(R.id.brightness))
            it.setButton_play(controllerNow?.findViewById(R.id.play))
            it.setButton_play1(controllerNow?.findViewById(R.id.play1))
            it.setButton_fastforward(controllerNow?.findViewById(R.id.fastforward))
            it.setButton_rewind(controllerNow?.findViewById(R.id.rewind))
            it.setText_tvRw(controllerNow?.findViewById(R.id.rwtext))
            it.setText_tvFf(controllerNow?.findViewById(R.id.fftext))
            it.setText_tvStart(controllerNow?.findViewById(R.id.tv_now))
            it.setText_tvEnd(controllerNow?.findViewById(R.id.tv_end))
            it.setidle_icon(controllerNow?.findViewById(R.id.idle_icon))
            it.setvolume_btn(controllerNow?.findViewById(R.id.btn_volume))
            it.setscreen_btn(controllerNow?.findViewById(R.id.btn_screen))
            it.setdownload_btn(controllerNow?.findViewById(R.id.btn_download))
            it.settv_title(controllerNow?.findViewById(R.id.title_dvr))
        }
        controllerNow?.findViewById<ImageView>(R.id.btn_volume)?.setOnClickListener {
            mDvrViewModel.dispatch(DvrViewAction.ChangeVolumeClicked)
        }
        controllerNow?.findViewById<ImageView>(R.id.btn_screen)?.setOnClickListener {
            mDvrViewModel.dispatch(DvrViewAction.ChangeScreenClicked)
        }
        controllerNow?.findViewById<ImageView>(R.id.btn_download)?.setOnClickListener {
            if (mDvrViewModel.viewStates.value.modeStatus == ModeStatus.Online) {
                requestFile()
                mDvrViewModel.dispatch(DvrViewAction.DownloadClicked())
            }
        }
        mControllerViewManager!!.initToolFanction()
        controllerNow?.findViewById<TextView>(R.id.title_dvr)?.text = mDvrViewModel.gettitle()
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun FullScreenControll() {
        val layoutParams: ViewGroup.LayoutParams = binding.ivideoview.layoutParams
        val layoutParams_b: ViewGroup.LayoutParams = binding.mediaBackground.layoutParams
        Log.e(TAG, "" + layoutParams.width)
        Log.e(TAG, "" + layoutParams.height)
        layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
        layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT
        layoutParams_b.width = ViewGroup.LayoutParams.MATCH_PARENT
        layoutParams_b.height = ViewGroup.LayoutParams.MATCH_PARENT
        binding.ivideoview.layoutParams = layoutParams
        binding.mediaBackground.layoutParams = layoutParams_b
        addControllView()
        ThemeManager.showBar(
            false,
            ThemeManager.autoChangeInt(Color.TRANSPARENT, "#2A3042".toColorInt())
        )
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun SingleScreenControll() {
        val layoutParams: ViewGroup.LayoutParams = binding.ivideoview.layoutParams
        val layoutParams_b: ViewGroup.LayoutParams = binding.mediaBackground.layoutParams
        Log.e(TAG, "" + layoutParams.width)
        Log.e(TAG, "" + layoutParams.height)
        layoutParams.width = 1080
        layoutParams.height = 608
        layoutParams_b.width = 1080
        layoutParams_b.height = 608
        binding.ivideoview.layoutParams = layoutParams
        binding.mediaBackground.layoutParams = layoutParams_b
        addControllView()
        ThemeManager.showBar(
            true,
            ThemeManager.autoChangeInt(Color.TRANSPARENT, "#2A3042".toColorInt())
        )
    }

    private fun initP2p(): Boolean {
        if (!context?.packageManager?.hasSystemFeature(PackageManager.FEATURE_WIFI_DIRECT)!!) {
            Log.e(TAG, "Wi-Fi Direct is not supported by this device.")
            return false
        }
        if (wifiManager == null) {
            Log.e(TAG, "Cannot get Wi-Fi system service.")
            return false
        }
        if (wifiManager?.isP2pSupported == false) {
            Log.e(TAG, "Wi-Fi Direct is not supported by the hardware or Wi-Fi is off.")
            return false
        }

        if (wifiP2pManager == null) {
            Log.e(TAG, "Cannot get Wi-Fi Direct system service.")
            return false
        }
        return true
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun onScreenChange(isFullScreenMode: Boolean) {
//                val parent = videoView!!.getParent()
        if (isFullScreenMode) {
            requireActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE)
//                    getActivity()!!.getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            requireActivity().window.setFlags(
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
            );//保持屏幕不变黑
            requireActivity().window.setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
            );//保持全屏，不显示系统状态栏
            binding.toptoolbar.visibility = View.GONE
            binding.onlineDvr.visibility = View.GONE
            binding.locateDvr.visibility = View.GONE
            binding.dvrList.visibility = View.GONE
            FullScreenControll()
        } else {
            binding.toptoolbar.visibility = View.VISIBLE
            binding.onlineDvr.visibility = View.VISIBLE
            binding.locateDvr.visibility = View.VISIBLE
            binding.dvrList.visibility = View.VISIBLE
            requireActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
//                    getActivity()!!.window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            requireActivity().window.setFlags(
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
            );//保持屏幕不变黑
            requireActivity().window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)//保持全屏，不显示系统状态栏
            SingleScreenControll()
        }
        mMediaController?.show()
    }

    private fun showDelectDialog(listener: DelectDialogListener): Dialog {
        val deleteConfirmationDialog = Dialog(requireContext(), R.style.POLICY_DIALOG)
        deleteConfirmationDialog.setContentView(com.link.riderlink.R.layout.dialog_delect_layout)
        deleteConfirmationDialog.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        deleteConfirmationDialog.show()
        val dialogTitleTextView = deleteConfirmationDialog.findViewById<TextView>(com.link.riderlink.R.id.ds_title)
        val dialogMessageTextView = deleteConfirmationDialog.findViewById<TextView>(com.link.riderlink.R.id.ds_text)
        val cancelButton = deleteConfirmationDialog.findViewById<ImageButton>(com.link.riderlink.R.id.select_cancel)
        val okButton = deleteConfirmationDialog.findViewById<ImageButton>(com.link.riderlink.R.id.select_ok)
        val dialogBackground =
            deleteConfirmationDialog.findViewById<ImageView>(com.link.riderlink.R.id.dialog_background)
        dialogBackground.setBackgroundResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                com.link.riderlink.R.drawable.connect_dialog_background
            )
        )
        cancelButton.setBackgroundResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                com.link.riderlink.R.drawable.delect_btn_cancel
            )
        )
        okButton.setBackgroundResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                com.link.riderlink.R.drawable.delect_btn_ok
            )
        )
        dialogTitleTextView.setTextColor(
            resources.getColor(
                ThemeManager.autoChangeInt(
                    com.link.riderlink.R.color.black_tx,
                    R.color.white
                ), null
            )
        )
        dialogMessageTextView.setTextColor(
            resources.getColor(
                ThemeManager.autoChangeInt(
                    com.link.riderlink.R.color.grey_tx,
                    R.color.white
                ), null
            )
        )
        cancelButton.setOnClickListener {
            deleteConfirmationDialog.dismiss()
        }
        okButton.setOnClickListener {
            listener.onOk()
            deleteConfirmationDialog.dismiss()
        }

        ThemeManager.registerThemeChangeListener(object : ThemeManager.OnThemeChangeListener() {
            override fun onThemeChanged() {
                dialogBackground.setBackgroundResource(
                    ThemeManager.getCurrentThemeRes(
                        requireContext(),
                        com.link.riderlink.R.drawable.connect_dialog_background
                    )
                )
                cancelButton.setBackgroundResource(
                    ThemeManager.getCurrentThemeRes(
                        requireContext(),
                        com.link.riderlink.R.drawable.delect_btn_cancel
                    )
                )
                okButton.setBackgroundResource(
                    ThemeManager.getCurrentThemeRes(
                        requireContext(),
                        com.link.riderlink.R.drawable.delect_btn_ok
                    )
                )
                dialogTitleTextView.setTextColor(
                    resources.getColor(
                        ThemeManager.autoChangeInt(
                            com.link.riderlink.R.color.black_tx,
                            R.color.white
                        ), null
                    )
                )
                dialogMessageTextView.setTextColor(
                    resources.getColor(
                        ThemeManager.autoChangeInt(
                            com.link.riderlink.R.color.grey_tx,
                            R.color.white
                        ), null
                    )
                )
            }

        })
        return deleteConfirmationDialog
    }

    override fun onStop() {
        super.onStop()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        changeSystemBarColorActually(Color.TRANSPARENT, Color.TRANSPARENT)
        videoView?.onDestroy()
        mDvrViewModel.pauseAll()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        requireActivity().window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
    }

    override fun onResume() {
        super.onResume()
        view?.setFocusableInTouchMode(true);
        view?.requestFocus();
        view?.setOnKeyListener(object : View.OnKeyListener {
            override fun onKey(view: View?, pressedKeyCode: Int, event: KeyEvent?): Boolean {
                if (pressedKeyCode == KeyEvent.KEYCODE_BACK && event?.action == KeyEvent.ACTION_UP) {
                    if (mDvrViewModel.viewStates.value.isFullscreen) {
                        mDvrViewModel.dispatch(DvrViewAction.ChangeScreenClicked)
                        return true
                    } else {
                        return false
                    }
                }
                return false;
            }
        });
    }

    fun initTheme() {
        binding.ibBack.setImageResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                com.link.riderlink.R.drawable.set_back
            )
        )
        
        // 使用主题颜色常量
        binding.dvrRoot.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
        binding.txTitle.setThemeTextColor(ThemeColors.PRIMARY_TEXT)
        binding.toptoolbar.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
        binding.onlineDvr.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
        binding.onlineDvr.onThemeChange(ThemeManager.isNightMode(requireContext()))
        binding.locateDvr.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
        binding.locateDvr.onThemeChange(ThemeManager.isNightMode(requireContext()))
        
        adapter.submitList(null)
        mDvrViewModel.viewStates.value.let {
            if (it.modeStatus is ModeStatus.Online) {
                adapter.submitList(it.onlineList)
            } else if (it.modeStatus is ModeStatus.Local) {
                adapter.submitList(it.localList)
            }
        }
        
        if (mDvrViewModel.viewStates.value.isFullscreen) {
            ThemeManager.showBar(
                false,
                ThemeManager.autoChangeInt(Color.TRANSPARENT, "#2A3042".toColorInt())
            )
        } else {
            ThemeManager.showBar(
                true,
                ThemeManager.autoChangeInt(Color.TRANSPARENT, "#2A3042".toColorInt())
            )
        }
    }

    val themeCallback = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }

    }

    override fun onPause() {
        super.onPause()
        mDvrViewModel.dispatch(DvrViewAction.PlayClicked(false))
        Log.e(TAG, "on pause")
    }

    interface DelectDialogListener {
        fun onCancel()
        fun onOk()
    }

    companion object {
        private const val TAG = "DvrFragment"
    }

}