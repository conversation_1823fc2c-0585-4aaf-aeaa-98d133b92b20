package com.link.riderlink.features.hide

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import com.link.riderlink.databinding.FragmentAutolinkversionBinding
import com.link.riderlink.ui.theme.ThemeHelper
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderlink.ui.theme.setupBackButton
import com.link.riderservice.api.RiderService

class AutoLinkVersionFragment : Fragment(){
    private var _binding: FragmentAutolinkversionBinding? = null
    private val binding get() = _binding!!
    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAutolinkversionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.llBack.setOnClickListener{
            popBackStack()
        }
        binding.txAutolinkversion.text = RiderService.instance.getAutolinkVersion()
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
    }
    fun initTheme(){
        // 使用Fragment扩展函数简化返回按钮设置
        setupBackButton(binding.ibBack)
        
        // 使用统一的版本页面主题设置
        ThemeHelper.setVersionPageTheme(
            binding.autolinkversionRoot,
            binding.tvTitle,
            binding.txAutolinkversion
        )
    }
    val themeCallback = object: ThemeManager.OnThemeChangeListener(){
        override fun onThemeChanged() {
            initTheme()
        }
    }
    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        _binding = null
    }
}