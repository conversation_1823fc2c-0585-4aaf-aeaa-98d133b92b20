package com.link.riderlink.features.privacy

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebSettings
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import com.link.riderlink.databinding.FragmentPrivacyBinding
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.extensions.isChina
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderlink.ui.theme.setThemeBackgroundColor
import com.link.riderlink.ui.theme.setThemeTextColor
import com.link.riderlink.ui.theme.setupBackButton

/**
 * <AUTHOR>
 * @date 2022/8/25
 */

class PrivacyFragment : Fragment() {
    private var _binding: FragmentPrivacyBinding? = null
    private val binding get() = _binding!!

    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPrivacyBinding.inflate(inflater, container, false)
        return binding.root
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val settings: WebSettings = binding.policy.settings
        settings.javaScriptEnabled = true
        settings.minimumFontSize = settings.minimumLogicalFontSize + 8
        binding.policy.loadUrl(PRIVACY_CN_URL)
        binding.llBack.setOnClickListener {
            popBackStack()
        }
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
    }

    fun initTheme() {
        // 使用Fragment扩展函数设置返回按钮
        setupBackButton(binding.ibBack)
        
        // 使用优化的主题设置 - 简化重复代码
        binding.privacyRoot.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
        binding.tvTitle.setThemeTextColor(ThemeColors.PRIMARY_TEXT)
        binding.policy.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
        
        // URL切换保持原有逻辑
        if (requireContext().isChina()) {
            binding.policy.loadUrl(ThemeManager.autoChangeStr(PRIVACY_CN_URL, PRIVACY_CN_URL_NIGHT))
        }else{
            binding.policy.loadUrl(ThemeManager.autoChangeStr(PRIVACY_URL, PRIVACY_URL_NIGHT))
        }
    }

    val themeCallback = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        _binding = null
    }

    companion object {
        private const val PRIVACY_URL = "file:///android_asset/privacy.html"
        private const val PRIVACY_URL_NIGHT = "file:///android_asset/privacy_night.html"
        private const val PRIVACY_CN_URL = "file:///android_asset/privacy_cn.html"
        private const val PRIVACY_CN_URL_NIGHT = "file:///android_asset/privacy_cn_night.html"
    }

}