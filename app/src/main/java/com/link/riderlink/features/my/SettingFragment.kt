package com.link.riderlink.features.my

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import com.hjq.toast.ToastUtils
import com.link.riderlink.R
import com.link.riderlink.RiderLink
import com.link.riderlink.databinding.FragmentSettingBinding
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.theme.ThemeHelper
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.theme.ThemeResourceHelper
import com.link.riderlink.ui.extensions.navigate
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderlink.ui.theme.setThemeBackgroundColor
import com.link.riderlink.ui.theme.setupBackButton
import com.link.riderlink.ui.components.dialogs.SelectDialog
import com.link.riderservice.api.RiderService

/**
 * <AUTHOR>
 * @date 2022/12/27
 */

class SettingFragment : Fragment() {
    private var _binding: FragmentSettingBinding? = null
    private val binding get() = _binding!!

    private val TAG = "SettingFragment"
    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val preference = context?.getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
        val modenight = preference?.getBoolean("mode_night", false) == true
        val followsystem = preference?.getBoolean("follow_system", false) == true
        binding.btnSwitchMode.isChecked = modenight
        binding.btnSwitchFollow.isChecked = if(modenight) followsystem else modenight
        binding.btnSwitchFollow.isEnabled = modenight
        binding.btnSwitchMode.setOnClickListener{
            val isChecked = binding.btnSwitchMode.isChecked
            binding.btnSwitchFollow.isEnabled = isChecked
            ThemeManager.themeMode =
                if(isChecked) ThemeManager.ThemeMode.NIGHT else ThemeManager.ThemeMode.DAY
            if (!isChecked) {
                if (preference != null) {
                    with (preference.edit()) {
                        putBoolean("follow_system", false)
                        apply()
                    }
                }
                binding.btnSwitchFollow.isChecked = false
            }
            if (preference != null) {
                with (preference.edit()) {
                    putBoolean("mode_night", isChecked)
                    apply()
                }
            }
        }
        binding.btnSwitchFollow.setOnClickListener{
            val isChecked = binding.btnSwitchFollow.isChecked
            ThemeManager.themeMode =
                if (ThemeManager.isSystemNightMode(requireContext())) ThemeManager.ThemeMode.NIGHT else ThemeManager.ThemeMode.DAY
            if (!isChecked) {
                ThemeManager.themeMode =
                    ThemeManager.ThemeMode.NIGHT
            }
            if (preference != null) {
                with (preference.edit()) {
                    putBoolean("follow_system", isChecked)
                    apply()
                }
            }
        }
//        binding.btnSwitchMode.isEnabled = !followsystem
//        binding.btnSwitchMode.setOnClickListener{
//            ThemeManager.themeMode = if(binding.btnSwitchMode.isChecked) ThemeManager.ThemeMode.NIGHT else ThemeManager.ThemeMode.DAY
//            if (preference != null) {
//                with (preference.edit()) {
//                    putBoolean("mode_night", binding.btnSwitchMode.isChecked)
//                    apply()
//                }
//            }
//        }
//        binding.btnSwitchFollow.setOnClickListener{
//            binding.btnSwitchMode.isEnabled = !binding.btnSwitchFollow.isChecked
//            if (binding.btnSwitchFollow.isChecked) {
//                ThemeManager.themeMode =
//                    if (ThemeManager.isSystemNightMode(requireContext())) ThemeManager.ThemeMode.NIGHT else ThemeManager.ThemeMode.DAY
//            }
//            else {
//                ThemeManager.themeMode =
//                    if (binding.btnSwitchMode.isChecked) ThemeManager.ThemeMode.NIGHT else ThemeManager.ThemeMode.DAY
//            }
//            if (preference != null) {
//                with (preference.edit()) {
//                    putBoolean("follow_system", binding.btnSwitchFollow.isChecked)
//                    apply()
//                }
//            }
//        }
        binding.llBack.setOnClickListener{
            popBackStack()
        }
        binding.itemHelp.setOnClickListener{
            navigate(R.id.action_settingFragment_to_helpFragment)
        }
        binding.itemPrivate.setOnClickListener{
            navigate(R.id.action_settingFragment_to_privacyFragment)
        }
        binding.itemAbout.setOnClickListener{
            navigate(R.id.action_settingFragment_to_aboutFragment)
        }
        binding.itemDel.setOnClickListener{
            val dialog = SelectDialog.Builder(requireActivity())
            val sharedPref = RiderService.instance.getApplication().getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
            val waddress = sharedPref.getString("wifi_address","")
            val baddress = sharedPref.getString("ble_address","")
            val port = sharedPref.getInt("wifi_port",0)
            var message = ""
            if (waddress != null&&port!=0) {
                message = "\nble:$baddress\nwifi_address:$waddress\nport:$port"
            }
            dialog
                .setTitle(getString(R.string.delete_config_title))
                .setMessage(getString(R.string.delete_config_message,message))
                .setOnConfirmClick {
                    RiderLink.instance.disconnect(false)
                    RiderService.instance.deleteConfig()
                    ToastUtils.show(getString(R.string.delete_config_success))
                }.show()
        }
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
    }
    val themeCallback = object: ThemeManager.OnThemeChangeListener(){
        override fun onThemeChanged() {
            initTheme()
        }
    }

    private fun initTheme() {
        // 使用Fragment扩展函数设置返回按钮
        setupBackButton(binding.ibBack)
        
        // 使用新的主题工具 - 大大简化代码！
        binding.setRoot.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
        
        // 批量设置所有设置项文字颜色
        ThemeHelper.setSettingPageTextTheme(
            binding.txTitle,
            binding.tvNight,
            binding.tvFollow,
            binding.tvHelp,
            binding.tvPrivate,
            binding.tvAbout,
            binding.tvDel
        )
        
        // 批量设置背景资源 - 4行简化为1行
        ThemeResourceHelper.setSettingPageBackgrounds(
            binding.background1,
            binding.background2, 
            binding.background3,
            binding.background4
        )
        
        // 批量设置图标 - 5行简化为1行
        ThemeResourceHelper.setSettingPageIcons(
            binding.setNight,
            binding.setHelp,
            binding.setPrivate,
            binding.setAbout,
            binding.setDel
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
    }
}