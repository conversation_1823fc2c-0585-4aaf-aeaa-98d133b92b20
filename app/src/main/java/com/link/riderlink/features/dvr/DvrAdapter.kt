package com.link.riderlink.features.dvr

import com.link.riderlink.ui.theme.ThemeColors
import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import android.view.LayoutInflater
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.hjq.toast.ToastUtils
import com.link.riderdvr.database.model.DownLoadTask
import com.link.riderdvr.databinding.DvrItemBinding
import com.link.riderdvr.utils.ThumbnailUtils
import com.link.riderdvr.utils.mainScope
import com.makeramen.roundedimageview.RoundedImageView
import kotlinx.coroutines.*
import java.util.*
import com.link.riderlink.ui.theme.ThemeResourceHelper


class DvrAdapter(
    private val onItemClick: (DvrItem, Int, Int) -> Unit?,
    private val onStartPauseClick: (DvrItem, Int) -> Unit,
    private val onItemDelete: (DvrItem, Int) -> Unit,
    private val getDownloadingTask: () -> DownLoadTask,
    private val getDownloadSpeed: () -> String,
    private val onDownloadClick: (DvrItem) -> Unit,
) : ListAdapter<DvrItem, DvrAdapter.DvrViewHolder>(DIFF_CALLBACK) {
    private var currentDvrItem: DvrItem?= null
    private var context: Context ?= null
    val bitmapCache = TreeMap<String,Bitmap>()
    val roundedImageViewCache = TreeMap<String, RoundedImageView>()
    val durationCache = TreeMap<String,String>()
    val textViewCache = TreeMap<String,TextView>()
    private var selectedPosition = 0 // 选中的位置
    private var isNight = false

    fun setSelectedPosition(position: Int) {
        selectedPosition = position
    }
    fun getSelectedPosition():Int {
        return selectedPosition
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DvrViewHolder {
        context = parent.context
        return DvrViewHolder(
            DvrItemBinding.inflate(
                LayoutInflater.from(parent.context), parent,
                false
            )
        )
    }
    fun putBitmapView(cacheKey:String, roundedImageView: RoundedImageView){
        roundedImageViewCache.put(cacheKey,roundedImageView)
    }
    fun putBitmap(cacheKey:String, iconImageView: Bitmap){
        bitmapCache.put(cacheKey,iconImageView)
    }
    fun putTextView(cacheKey:String, textView: TextView){
        textViewCache.put(cacheKey,textView)
    }
    fun putDuration(cacheKey:String, duration: String){
        durationCache.put(cacheKey,duration)
    }
    fun setDvrItem(itemContainerLayout: DvrItem){
        currentDvrItem = itemContainerLayout
    }
    fun getDvrItem(itemContainerLayout: DvrItem){
        currentDvrItem = itemContainerLayout
    }
    inner class DvrViewHolder(binding: DvrItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        private val titleTextView: TextView = binding.tvDvrName
        private val durationTextView: TextView = binding.tvDvrDuration
        private val itemContainerLayout: ConstraintLayout = binding.clDvrItem
        private val deleteButton: ImageView = binding.scrollableDvr
        private val downloadProgressTextView: TextView = binding.downloadProgressText
        private val downloadProgressBar: ProgressBar = binding.downloadProgress
        private val downloadSpeedTextView: TextView = binding.downloadSpeed
        private val iconImageView: RoundedImageView = binding.tvDvrIcon

        private val playingIndicatorImageView: ImageView = binding.signPlay
        private val downloadedIndicatorImageView: ImageView = binding.signDownload
        private val copyButton: ImageView = binding.btnCopy
        private val downloadButton: ImageView = binding.btnDownload
        @SuppressLint("SuspiciousIndentation", "ServiceCast", "SetTextI18n")
        fun bind(dvrItem: DvrItem, position: Int) {
//            downloadProgressBar.scrollBarStyle = View.SCROLLBARS_INSIDE_OVERLAY
            Log.e(TAG + 99, "bind: $dvrItem")
            titleTextView.text = dvrItem.title
            <EMAIL> {
                if (!(roundedImageViewCache.get(""+dvrItem.databaseID+dvrItem.title) == null || durationCache.get(""+dvrItem.databaseID+dvrItem.title)== null)) {
                    val old = selectedPosition
                    setSelectedPosition(position)
//                Log.e("79$TAG","ONCLICK11111111111:"+position)
                    if (dvrItem.mode != "task") {
                        onItemClick(dvrItem, old, position)
                    } else {
//                    Log.e("79$TAG","ONCLICK11111111111:"+selectedPosition)
                        onStartPauseClick(dvrItem, selectedPosition)
                    }
                }
            }
            downloadProgressTextView.setOnClickListener {
                onStartPauseClick(dvrItem, selectedPosition)
            }
            downloadButton.setOnClickListener{
                onDownloadClick(dvrItem)
            }
            deleteButton.setOnClickListener {
                onItemDelete(dvrItem, position)
            }
            copyButton.setOnClickListener{
                val clipboardManager = context?.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                //clipData中的this就是需要复制的文本
                val clipData = ClipData.newPlainText("", dvrItem.path)
                clipboardManager.setPrimaryClip(clipData)
                ToastUtils.show("复制成功")
            }
//            downloadProgressBar.visibility = if(dvrItem.mode == "task") VISIBLE else GONE
            downloadSpeedTextView.visibility = if(dvrItem.mode == "online"&&dvrItem.isDownloading) VISIBLE else GONE
            deleteButton.visibility = if(dvrItem.isDownloaded||dvrItem.isDownloading) VISIBLE else GONE
            downloadedIndicatorImageView.visibility = if(dvrItem.isDownloaded&&!dvrItem.isSelected) VISIBLE else GONE
            playingIndicatorImageView.visibility = if(dvrItem.isSelected) VISIBLE else GONE
            downloadButton.visibility = if(!dvrItem.isDownloading&&!dvrItem.isDownloaded) VISIBLE else GONE
            copyButton.visibility = if (dvrItem.isDownloaded) VISIBLE else GONE
//            Log.e(TAG, "bind: ${dvrItem.isDownloading},${dvrItem.isdownload}", )
            copyButton.visibility = if(dvrItem.isDownloaded) VISIBLE else GONE
            downloadProgressBar.visibility = if (dvrItem.isDownloading) VISIBLE else GONE
            downloadProgressTextView.visibility = downloadProgressBar.visibility
            if (dvrItem.downloadStatus == "downloading"){
                downloadProgressBar.progress = dvrItem.progress
                downloadSpeedTextView.text = getDownloadSpeed()
                downloadProgressTextView.text = dvrItem.progress.toString()+"%"
//                    Log.e(TAG +"104",dvrItem.title+":"+titleTextView.text+":"+position+":"+getDownloadingTask().progress)
            }else if(dvrItem.downloadStatus == "pause"){
                downloadProgressBar.progress = dvrItem.progress
                downloadProgressTextView.text = context?.getString(com.link.riderlink.R.string.pause)
            }else if(dvrItem.downloadStatus == "prepare"){
                downloadProgressBar.progress = dvrItem.progress
                downloadProgressTextView.text = context?.getString(com.link.riderlink.R.string.prepare)
            }
            if(dvrItem.progress == 100) {
                downloadProgressTextView.visibility = GONE
                downloadProgressBar.visibility = GONE
                copyButton.visibility = if (dvrItem.isDownloaded) VISIBLE else GONE
            }
            putBitmapView(""+dvrItem.databaseID+dvrItem.title, iconImageView)
            putTextView(""+dvrItem.databaseID+dvrItem.title, durationTextView)
        }

        fun bind1(dvrItem: DvrItem){
//            Log.e("121$TAG",""+dvrItem)
            if(dvrItem.mode.equals("task")&& dvrItem.progress <100){
                if(dvrItem.downloadStatus == "downloading" || dvrItem.downloadStatus == "prepare") {
                    iconImageView.setImageResource(com.link.riderdvr.R.drawable.pause)
                }
                else if(dvrItem.downloadStatus == "pause") {
                    iconImageView.setImageResource(com.link.riderdvr.R.drawable.play)
                }
                return
            }
            Log.e("132$TAG",""+dvrItem)

            mainScope.launch(Dispatchers.IO) {
                if(roundedImageViewCache.get(""+dvrItem.databaseID+dvrItem.title) == null || durationCache.get(""+dvrItem.databaseID+dvrItem.title)== null) {
                    getVideoThumbnailBitmap(dvrItem.databaseID,dvrItem.title,dvrItem.url,dvrItem.mode,dvrItem, 50,50,ThumbnailUtils.MINI_KIND)
                    getVideoThumbnailDuration(dvrItem)
                }
                val iconImageView = roundedImageViewCache.get(""+dvrItem.databaseID+dvrItem.title)
                val bitmap = bitmapCache.get(""+dvrItem.databaseID+dvrItem.title)
                val durationTextView = textViewCache.get(""+dvrItem.databaseID+dvrItem.title)
                val duration = durationCache.get(""+dvrItem.databaseID+dvrItem.title)
                mainScope.launch(Dispatchers.Main){
                    iconImageView?.setImageBitmap(bitmap)
                    durationTextView?.text = duration
                }
            }
//            mainScope.launch(Dispatchers.IO) {
//                if(roundedImageViewCache.get(""+dvrItem.databaseID+dvrItem.title) == null || durationCache.get(""+dvrItem.databaseID+dvrItem.title)== null) {
//                    delay(100)//不增加延迟，列表无法在缩略图请求完成之前出现
//                    getVideoThumbnailDuration(dvrItem.databaseID,dvrItem.title,dvrItem.url,dvrItem.mode,dvrItem, 50,50,ThumbnailUtils.MINI_KIND)
//                }
//                val dvrduration = textViewCache.get(""+dvrItem.databaseID+dvrItem.title)
//                val duration = durationCache.get(""+dvrItem.databaseID+dvrItem.title)
//                mainScope.launch(Dispatchers.Main){
//                    dvrduration?.text = duration
//                }
//            }
        }

        fun initTheme(){
            // 使用主题颜色常量
            titleTextView.setTextColor(ThemeColors.ADAPTER_TEXT.getCurrentColorInt(context!!))
            
            // 使用批量图标设置工具 - 2行简化为1行
            ThemeResourceHelper.setDvrAdapterIcons(copyButton, downloadButton)
        }
    }

    suspend fun getVideoThumbnailBitmap(dvrId: Int, title:String, path: String?, mode:String, itemContainerLayout: DvrItem, width: Int, height: Int, kind: Int) = coroutineScope {
        var bitmap: Bitmap? = null
        // 获取视频的缩略图
        Log.e(TAG,"148:StartGet"+itemContainerLayout.url)
//        Log.e(TAG,"145:StartGet"+itemContainerLayout)
        if(itemContainerLayout.mode != "task") {
            bitmap = ThumbnailUtils.instance.createVideoThumbnail(
                itemContainerLayout.url,
                kind, itemContainerLayout.mode == "online"
            ) //調用ThumbnailUtils類的靜態方法createVideoThumbnail獲取視頻的截圖；
        }
        else {
            bitmap = ThumbnailUtils.instance.createVideoThumbnail(
                itemContainerLayout.path,
                kind, itemContainerLayout.isDownloaded
            ) //調用ThumbnailUtils類的靜態方法createVideoThumbnail獲取視頻的截圖；
        }
        if (bitmap != null) {
            putBitmap("${itemContainerLayout.databaseID}"+itemContainerLayout.title,bitmap)
        }else{
            Log.e(TAG,"false")
        }
    }
    suspend fun getVideoThumbnailDuration(itemContainerLayout: DvrItem) = coroutineScope {
        var duration = ""
        // 获取视频的缩略图
        Log.e(TAG,"148:StartGet"+itemContainerLayout.url)
//        Log.e(TAG,"145:StartGet"+itemContainerLayout)
        if(itemContainerLayout.mode != "task") {
            duration = ThumbnailUtils.instance.getGenerateTime(ThumbnailUtils.instance.getDuration(itemContainerLayout.url, itemContainerLayout.mode == "online"))
        }
        else {
            duration = ThumbnailUtils.instance.getGenerateTime(ThumbnailUtils.instance.getDuration(itemContainerLayout.url, false))
        }
        if (duration != "") {
            putDuration("${itemContainerLayout.databaseID}"+itemContainerLayout.title,duration)
        }else{
            Log.e(TAG,"false")
        }
    }
    override fun onBindViewHolder(holder: DvrViewHolder, position: Int) {
        val item = getItem(position)
        holder.bind(item, position)
        holder.bind1(item)
        holder.initTheme()
    }

    companion object {
        private const val TAG = "DvrAdapter"
        private val DIFF_CALLBACK: DiffUtil.ItemCallback<DvrItem> =
            object : DiffUtil.ItemCallback<DvrItem>() {
                override fun areItemsTheSame(
                    oldItem: DvrItem,
                    newItem: DvrItem,
                ): Boolean {
                    return oldItem.mode == newItem.mode&&oldItem.url == newItem.url&&oldItem.title == newItem.title&&oldItem.progress == newItem.progress&&oldItem.downloadStatus == newItem.downloadStatus&&oldItem.isSelected == newItem.isSelected
                }

                override fun areContentsTheSame(
                    oldItem: DvrItem,
                    newItem: DvrItem,
                ): Boolean {
                    return oldItem.progress == newItem.progress
                }
            }
    }
}