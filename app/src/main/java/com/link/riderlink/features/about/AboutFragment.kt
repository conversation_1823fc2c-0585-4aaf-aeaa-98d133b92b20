package com.link.riderlink.features.about

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import com.link.riderlink.R
import com.link.riderlink.databinding.FragmentAboutBinding
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.theme.setupBackButton
import com.link.riderlink.ui.theme.setThemeTextColor
import com.link.riderlink.ui.theme.setThemeBackgroundColor
import com.link.riderlink.ui.extensions.navigate
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderservice.core.extensions.coroutines.mainScope
import com.link.riderservice.core.utils.countDownByFlow
import com.link.riderservice.core.utils.system.Platform
import kotlinx.coroutines.Job

/**
 * <AUTHOR>
 * @date 2022/10/20
 * @desc 关于界面
 */

class AboutFragment : Fragment() {
    private var _binding: FragmentAboutBinding? = null
    private val binding get() = _binding!!

    private var clickCountDownJob: Job? = null

    private var isCountingDownForImageClick = false

    private var imageClickCount = 0
    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAboutBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.appVersionTv.text = Platform.packageName(requireContext())
        binding.llBack.setOnClickListener {
            popBackStack()
        }
        binding.imageView5.setOnClickListener {
            onImageClick()
        }
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
    }

    private fun onImageClick() {
        if (isCountingDownForImageClick){
            imageClickCount++
        }else{
            startClickCountDown()
        }
    }

    private fun startClickCountDown() {
        isCountingDownForImageClick = true
        imageClickCount = 1
        clickCountDownJob = countDownByFlow(80, 100, mainScope,
            onTick = {
                if (imageClickCount > 7) {
                    isCountingDownForImageClick = false
                    clickCountDownJob?.cancel()
                    imageClickCount = 0
                    navigate(R.id.action_aboutFragment_to_hidedebugFragment)
                }
            }, onFinish = {
                isCountingDownForImageClick = false
                imageClickCount = 0
            })
    }

    fun initTheme() {
        setupBackButton(binding.ibBack)
        
        binding.aboutRoot.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
        setThemeTextColor(
            ThemeColors.PRIMARY_TEXT,
            binding.tvTitle,
            binding.appNameTv,
            binding.appVersionTv
        )
        binding.copyrightTv.setThemeTextColor(ThemeColors.SECONDARY_TEXT)
    }
    val themeCallback = object: ThemeManager.OnThemeChangeListener(){
        override fun onThemeChanged() {
            initTheme()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        _binding = null
    }
}