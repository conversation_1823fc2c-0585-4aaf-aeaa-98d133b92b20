package com.link.riderlink.features.agreement

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebSettings
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import com.link.riderlink.databinding.FragmentAgreementBinding
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.theme.setThemeBackgroundColor
import com.link.riderlink.ui.extensions.popBackStack

/**
 * <AUTHOR>
 * @date 2022/8/25
 */

class AgreementFragment : Fragment() {
    private var _binding: FragmentAgreementBinding? = null
    private val binding get() = _binding!!

    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
           popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAgreementBinding.inflate(inflater, container, false)
        return binding.root
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val settings: WebSettings = binding.agreement.settings
        settings.javaScriptEnabled = true
        settings.minimumFontSize = settings.minimumLogicalFontSize + 8
        binding.agreement.loadUrl(AGREEMENT_CN_URL)
        initTheme()
    }

    fun initTheme() {
        // 使用主题颜色常量
        binding.privacyRoot.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        private const val AGREEMENT_CN_URL = "file:///android_asset/agreement_cn.html"
    }

}