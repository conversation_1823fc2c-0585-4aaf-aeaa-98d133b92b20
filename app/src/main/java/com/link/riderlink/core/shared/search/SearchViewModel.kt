package com.link.riderlink.features.search

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.link.riderhere.api.dto.SearchResult
import com.link.riderlink.RiderLink
import com.link.riderlink.features.search.data.repository.SearchRepositoryImpl
import com.link.riderlink.features.search.data.source.local.LocalSearchDataSourceImpl
import com.link.riderlink.features.search.data.source.local.db.model.SearchAddress
import com.link.riderlink.utils.SharedFlowEvents
import com.link.riderlink.utils.setState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 搜索功能的 ViewModel，负责处理搜索相关的业务逻辑
 */
class SearchViewModel : ViewModel() {
    private val viewStateInternal = MutableStateFlow(SearchViewState())
    val viewStates = viewStateInternal.asStateFlow()

    private val viewEventsInternal = SharedFlowEvents<SearchViewEvent>()
    val viewEvents = viewEventsInternal.asSharedFlow()

    private val searchRepository = SearchRepositoryImpl(LocalSearchDataSourceImpl())
    
    private val TAG = "SearchViewModel"

    /**
     * 处理视图动作
     */
    fun dispatch(viewAction: SearchViewAction) {
        when (viewAction) {
            is SearchViewAction.LoadHistories -> loadHistories()
            is SearchViewAction.InsertHistory -> insertHistory(viewAction.searchAddress)
            is SearchViewAction.DeleteHistory -> deleteHistory(viewAction.searchAddress)
            is SearchViewAction.Search -> performSearch(viewAction.keyword)
        }
    }

    /**
     * 加载历史记录
     */
    private fun loadHistories() {
        viewModelScope.launch {
            searchRepository.loadHistories().collect { histories ->
                viewStateInternal.setState { copy(histories = histories) }
            }
        }
    }

    /**
     * 插入历史记录
     */
    private fun insertHistory(searchAddress: SearchAddress) {
        viewModelScope.launch {
            searchRepository.insertHistory(searchAddress)
        }
    }

    /**
     * 删除历史记录
     */
    private fun deleteHistory(searchAddress: SearchAddress) {
        viewModelScope.launch {
            searchRepository.deleteHistory(searchAddress)
        }
    }

    /**
     * 执行搜索操作
     */
    private fun performSearch(keyword: String) {
        viewModelScope.launch {
            val result = search(keyword)
            result.searchList?.mapNotNull { searchItem ->
                searchItem.geoCoordinates?.let {
                    SearchAddress(
                        ad_code = searchItem.address.countryCode,
                        district = searchItem.address.district,
                        name = searchItem.title,
                        poi_id = searchItem.address.postalCode,
                        point_latitude = it.latitude.toString(),
                        point_longitude = it.longitude.toString(),
                    )
                }
            }?.let { searchAddresses ->
                viewStateInternal.setState {
                    copy(result = searchAddresses)
                }
            }
        }
    }

    /**
     * 调用搜索 API
     */
    private suspend fun search(keyword: String): SearchResult = RiderLink.instance.search(keyword)
}