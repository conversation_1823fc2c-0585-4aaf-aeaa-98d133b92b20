package com.link.riderlink.core.shared.search.ui

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import androidx.appcompat.app.AlertDialog
import androidx.core.graphics.drawable.toDrawable
import androidx.core.graphics.toColorInt
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.link.riderlink.R
import com.link.riderlink.core.database.model.SearchAddress
import com.link.riderlink.databinding.SearchDialogBinding
import com.link.riderlink.ui.components.lists.RecyclerViewHolder
import com.link.riderlink.ui.components.lists.ViewBinder
import com.link.riderlink.ui.components.lists.ViewRecyclerAdapter
import com.link.riderlink.ui.extensions.debounceClick
import com.link.riderlink.ui.extensions.observeState
import com.link.riderlink.ui.extensions.textChangeFlow
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.theme.ThemeManager
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.launch
import java.util.regex.Pattern

/**
 * 搜索对话框的基础Fragment，包含通用的UI逻辑
 * 子类需要提供具体的ViewModel实现
 */
abstract class BaseSearchDialogFragment(
    private val onAddressSelected: (SearchAddress) -> Unit
) : DialogFragment() {

    companion object {
        private const val TAG = "BaseSearchDialogFragment"
        private const val SEARCH_DEBOUNCE_TIME = 300L
        private const val HIGHLIGHT_COLOR = "#5C7BD7"
        
        /**
         * 隐藏键盘
         */
        fun hideKeyboard(activity: Activity) {
            val inputMethodManager = activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            inputMethodManager.hideSoftInputFromWindow(activity.currentFocus?.windowToken, 0)
        }

        /**
         * 显示键盘
         */
        fun showKeyboard(activity: Activity, view: View) {
            view.requestFocus()
            val inputMethodManager = activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            inputMethodManager.showSoftInput(view, 0)
        }
    }

    private var bindingReference: SearchDialogBinding? = null
    protected val binding get() = bindingReference!!

    private val historyAdapter = ViewRecyclerAdapter()
    private val searchAdapter = ViewRecyclerAdapter()
    private var keyword: String = ""

    // 抽象方法：子类提供具体的ViewModel
    protected abstract fun createSearchViewModel(): BaseSearchViewModel
    private lateinit var searchViewModel: BaseSearchViewModel

    // 生命周期方法
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        bindingReference = SearchDialogBinding.inflate(layoutInflater)
        val dialog = AlertDialog.Builder(requireContext(), R.style.SearchDialog)
            .setView(binding.root)
            .create()

        dialog.window?.apply {
            setGravity(Gravity.BOTTOM)
            WindowCompat.setDecorFitsSystemWindows(this, false)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                isNavigationBarContrastEnforced = false
            }
            navigationBarColor = Color.TRANSPARENT
            statusBarColor = Color.TRANSPARENT
        }
        return dialog
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, R.style.SearchDialog)
        searchViewModel = createSearchViewModel()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding.root.setOnApplyWindowInsetsListener { view, insets ->
            val layoutParams = view.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.setMargins(0, insets.systemWindowInsetTop, 0, 0)
            view.layoutParams = layoutParams
            insets.consumeSystemWindowInsets()
        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupUI()
        setupListeners()
        setupObservers()
        searchViewModel.dispatch(BaseSearchViewAction.LoadHistories)
        
        setupDialogWindow()
        initTheme()
        
        // 自动聚焦搜索框并显示键盘
        Handler(Looper.getMainLooper()).postDelayed({
            binding.tvAddressName.requestFocus()
            showSoftInput()
        }, 100)
        
        // 注册主题变化监听器
        ThemeManager.registerThemeChangeListener(themeCallback)
    }
    
    /**
     * 设置对话框窗口属性
     */
    private fun setupDialogWindow() {
        dialog?.window?.apply {
            setWindowAnimations(R.style.BottomDialog_Animation)
            setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        bindingReference = null
    }

    override fun onDestroy() {
        super.onDestroy()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        Log.d(TAG, "onDestroy")
    }

    // 主题相关
    private val themeCallback = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }

    /**
     * 初始化主题
     */
    protected open fun initTheme() {
        binding.run { 
            searchDialogBackgroundImg.setBackgroundResource(
                ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.search_background)
            )
            tvAddressName.setTextColor(resources.getColor(R.color.black_tx, null))
            
            // 刷新列表以应用新主题
            notifySearchList(searchViewModel.viewStates.value.result)
            notifyHistoryList(searchViewModel.viewStates.value.histories)
        }
    }

    /**
     * 设置UI初始状态
     */
    @OptIn(FlowPreview::class, ExperimentalCoroutinesApi::class)
    private fun setupUI() {
        binding.apply {
            tvAddressName.setText("")
            
            historyView.run {
                layoutManager = LinearLayoutManager(requireContext())
                adapter = historyAdapter
            }
            
            searchResultView.run {
                layoutManager = LinearLayoutManager(requireContext())
                adapter = searchAdapter
            }
        }
    }
    
    /**
     * 设置点击和按键监听器
     */
    private fun setupListeners() {
        binding.apply {
            ibBack.setOnClickListener {
                hideSoftInput()
                dismiss()
            }
            
            searchCancel.setOnClickListener {
                hideSoftInput()
                dismiss()
            }
            
            searchBox.setOnKeyListener { _, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_UP) {
                    val trimmedText = tvAddressName.text.toString().trim()
                    if (!TextUtils.isEmpty(trimmedText)) {
                        hideSoftInput()
                        searchViewModel.dispatch(
                            BaseSearchViewAction.InsertHistory(
                                SearchAddress(name = trimmedText, type = 0)
                            )
                        )
                        return@setOnKeyListener true
                    }
                }
                false
            }
        }
    }
    
    /**
     * 设置数据观察者
     */
    @OptIn(FlowPreview::class, ExperimentalCoroutinesApi::class)
    private fun setupObservers() {
        // 监听搜索框文本变化
        lifecycleScope.launch {
            binding.tvAddressName.textChangeFlow()
                .filter { text ->
                    val isNotEmpty = text.isNotEmpty()
                    binding.viewAnimator.displayedChild = if (isNotEmpty) 1 else 0
                    if (!isNotEmpty) notifySearchList(null)
                    Log.e(TAG, "setupObservers: $isNotEmpty")
                    isNotEmpty
                }
                .debounce(SEARCH_DEBOUNCE_TIME)
                .mapLatest { newKeyword ->
                    Log.e(TAG, "setupObservers: $newKeyword")
                    keyword = newKeyword.toString()
                    searchViewModel.dispatch(BaseSearchViewAction.Search(keyword))
                }
                .collect {}
        }

        // 监听ViewModel状态变化
        searchViewModel.viewStates.apply {
            observeState(viewLifecycleOwner, BaseSearchViewState::histories) {
                updateHistoryList(it)
            }
            
            observeState(viewLifecycleOwner, BaseSearchViewState::result) {
                notifySearchList(it)
            }
            
            observeState(viewLifecycleOwner, BaseSearchViewState::error) { error ->
                error?.let {
                    onError(it)
                }
            }
        }
    }
    
    /**
     * 错误处理，子类可以重写
     */
    protected open fun onError(message: String) {
        Log.e(TAG, "Search error: $message")
        // 子类可以显示Toast或其他错误提示
    }
    
    /**
     * 更新历史记录列表
     */
    private fun updateHistoryList(histories: List<SearchAddress>) {
        historyAdapter.clear()
        binding.historyView.visibility = if (histories.isEmpty()) View.GONE else View.VISIBLE
        histories.forEach { selectedAddress ->
            historyAdapter.add(HistoryViewBinder(selectedAddress))
        }
    }

    /**
     * 显示软键盘
     */
    private fun showSoftInput() {
        binding.tvAddressName.apply {
            isFocusable = true
            isFocusableInTouchMode = true
            requestFocus()
        }
        
        if (Build.VERSION.SDK_INT >= 30) {
            WindowInsetsControllerCompat(requireDialog().window!!, binding.tvAddressName)
                .show(WindowInsetsCompat.Type.ime())
        } else {
            val inputMethodManager = context?.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
            inputMethodManager?.showSoftInput(binding.tvAddressName, InputMethodManager.SHOW_IMPLICIT)
        }
    }

    /**
     * 隐藏软键盘
     */
    private fun hideSoftInput() {
        if (Build.VERSION.SDK_INT >= 30) {
            WindowInsetsControllerCompat(requireDialog().window!!, binding.tvAddressName)
                .hide(WindowInsetsCompat.Type.ime())
        } else {
            val inputMethodManager = context?.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
            inputMethodManager?.hideSoftInputFromWindow(binding.tvAddressName.windowToken, 0)
        }
    }

    /**
     * 更新搜索结果列表
     */
    private fun notifySearchList(searchResult: List<SearchAddress>?) {
        searchAdapter.clear()
        searchResult?.forEach {
            searchAdapter.add(SearchViewBinder(it))
        }
    }

    /**
     * 更新历史记录列表
     */
    private fun notifyHistoryList(historyResult: List<SearchAddress>?) {
        historyAdapter.clear()
        historyResult?.forEach {
            historyAdapter.add(HistoryViewBinder(it))
        }
    }

    /**
     * 执行搜索并处理结果
     */
    private fun search(selectedAddress: SearchAddress?) {
        selectedAddress?.let {
            onAddressSelected(it)
            searchViewModel.dispatch(BaseSearchViewAction.InsertHistory(selectedAddress))
            hideSoftInput()
            dismiss()
        }
    }
    
    /**
     * 显示删除确认对话框
     */
    private fun showDeleteConfirmDialog(addressToDelete: SearchAddress?) {
        AlertDialog.Builder(requireContext()).apply {
            setTitle("提示")
            setMessage("是否删除这条记录")
            setPositiveButton("确定") { _, _ ->
                addressToDelete?.let {
                    searchViewModel.dispatch(BaseSearchViewAction.DeleteHistory(it))
                }
            }
            setNegativeButton("取消") { _, _ -> }
        }.create().show()
    }

    /**
     * 历史记录ViewBinder
     */
    inner class HistoryViewBinder(selectedAddress: SearchAddress?) :
        ViewBinder<SearchAddress?>(selectedAddress) {

        override fun onCreateView(
            recyclerViewHolder: RecyclerViewHolder,
            addressData: SearchAddress?
        ) {
            setupCommonViewProperties(recyclerViewHolder, addressData)
            recyclerViewHolder.setImageResource(R.id.iv_type, R.drawable.item_history)
            
            recyclerViewHolder.itemView.setOnLongClickListener {
                showDeleteConfirmDialog(addressData)
                true
            }
        }

        override val binderLayout: Int
            get() = R.layout.search_dialog_list_item
    }

    /**
     * 搜索结果ViewBinder
     */
    inner class SearchViewBinder(
        selectedAddress: SearchAddress?
    ) : ViewBinder<SearchAddress?>(selectedAddress) {
        override val binderLayout: Int
            get() = R.layout.search_dialog_list_item

        override fun onCreateView(
            recyclerViewHolder: RecyclerViewHolder,
            addressData: SearchAddress?
        ) {
            setupCommonViewProperties(recyclerViewHolder, addressData)
            recyclerViewHolder.setImageResource(R.id.iv_type, R.drawable.item_location)
        }
    }
    
    /**
     * 设置共同的ViewHolder属性
     */
    private fun setupCommonViewProperties(recyclerViewHolder: RecyclerViewHolder, addressData: SearchAddress?) {
        recyclerViewHolder.run {
            setText(R.id.tv_address, addressData?.name)
            
            setImageResource(
                R.id.img_go_route,
                ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.item_route)
            )
            
            setTextColor(
                R.id.tv_address,
                ThemeColors.SEARCH_TEXT.getCurrentColorInt(requireContext())
            )
            
            setTextSpan(
                R.id.tv_address,
                stringToHighLight(addressData?.name.orEmpty())
            )
            
            itemView.debounceClick {
                search(addressData)
            }
        }
    }

    /**
     * 关键字高亮显示
     * @param text 原文
     * @param highlightIndividualChars 是否需要做分词高亮展示
     *              highlightIndividualChars = true  关键字里的每一个字，只要有都会高亮
     *              highlightIndividualChars = false（默认） 只有整词才会高亮
     */
    private fun stringToHighLight(text: String, highlightIndividualChars: Boolean = false): SpannableStringBuilder {
        val spannable = SpannableStringBuilder(text)
        if (keyword.isEmpty()) return spannable
        
        try {
            val keywordList = if (highlightIndividualChars) keyword.map { it.toString() } else listOf(keyword)
            
            keywordList.forEach { singleKeyword ->
                val escapedKeyword = Pattern.quote(singleKeyword)
                val pattern = Pattern.compile("(?i)$escapedKeyword")
                val matcher = pattern.matcher(text)
                
                while (matcher.find()) {
                    spannable.setSpan(
                        ForegroundColorSpan(HIGHLIGHT_COLOR.toColorInt()),
                        matcher.start(),
                        matcher.end(),
                        Spannable.SPAN_MARK_MARK
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "stringToHighLight-Error: ${e.message}")
        }
        
        return spannable
    }
} 