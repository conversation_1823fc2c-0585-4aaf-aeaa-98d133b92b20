package com.link.riderlink.core.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.link.riderlink.core.database.dao.UserHistoryDao
import com.link.riderlink.core.database.model.SearchAddress

/**
 * <AUTHOR>
 * @date 2022/6/29
 * @desc 数据库用来存储用户的搜索历史
 */
@Database(entities = [SearchAddress::class], version = 1, exportSchema = false)
abstract class AppDatabase : RoomDatabase() {
    abstract fun userHistoryDao(): UserHistoryDao

    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null

        fun getInstance(context: Context): AppDatabase =
            INSTANCE ?: synchronized(this) {
                INSTANCE ?: buildDatabase(context).also { INSTANCE = it }
            }

        private fun buildDatabase(context: Context) =
            Room.databaseBuilder(
                context.applicationContext,
                AppDatabase::class.java, DATABASE_NAME
            ).build()

        private const val DATABASE_NAME = "rider"
    }
}