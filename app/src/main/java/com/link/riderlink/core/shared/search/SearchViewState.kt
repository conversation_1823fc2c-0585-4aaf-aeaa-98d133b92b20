package com.link.riderlink.features.search

import com.link.riderhere.api.dto.SearchResult
import com.link.riderlink.features.search.data.source.local.db.model.SearchAddress

/**
 * 搜索页面的视图状态
 * 
 * @property histories 历史搜索记录列表
 * @property result 搜索结果列表
 * @property poiResultData 原始搜索结果数据
 */
data class SearchViewState(
    val histories: List<SearchAddress> = emptyList(),
    val result: List<SearchAddress> = emptyList(),
    val poiResultData: SearchResult = SearchResult(0, null, null)
)

/**
 * 搜索页面的事件
 */
sealed class SearchViewEvent

/**
 * 搜索页面的操作
 */
sealed class SearchViewAction {
    /**
     * 搜索操作
     * @param keyword 搜索关键词
     */
    data class Search(val keyword: String) : SearchViewAction()
    
    /**
     * 插入历史记录操作
     * @param searchAddress 要插入的地址
     */
    data class InsertHistory(val searchAddress: SearchAddress) : SearchViewAction()
    
    /**
     * 删除历史记录操作
     * @param searchAddress 要删除的地址
     */
    data class DeleteHistory(val searchAddress: SearchAddress) : SearchViewAction()
    
    /**
     * 加载历史记录操作
     */
    object LoadHistories : SearchViewAction()
}