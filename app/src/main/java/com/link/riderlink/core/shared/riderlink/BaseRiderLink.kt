package com.link.riderlink.core.shared.riderlink

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjection
import android.util.Log
import android.view.Display
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.core.content.edit
import androidx.fragment.app.FragmentActivity
import com.link.riderdvr.utils.mainScope
import com.link.riderlink.ui.components.dialogs.SelectDialog
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.utils.system.AppBackgroundManager
import com.link.riderlink.utils.system.ScreenBrightnessUtils
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.callback.RiderServiceCallback
import com.link.riderservice.api.dto.ArriveDestination
import com.link.riderservice.api.dto.BleStatus
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.api.dto.NaviInfo
import com.link.riderservice.api.dto.NaviMode
import com.link.riderservice.api.dto.NaviStart
import com.link.riderservice.api.dto.NaviStop
import com.link.riderservice.api.dto.NaviText
import com.link.riderservice.api.dto.RiderMessage
import com.link.riderservice.api.dto.RiderServiceConfig
import com.link.riderservice.feature.connection.ble.BleDevice
import com.link.riderservice.protobuf.RiderProtocol.NaviModeChangeResult
import kotlinx.coroutines.launch
import me.jessyan.autosize.utils.AutoSizeUtils

/**
 * RiderLink 抽象基类
 * 包含所有通用功能，具体地图实现由子类完成
 */
abstract class BaseRiderLink {

    // 配置和状态相关变量
    protected var requiresNavigationStart: Boolean = false
    protected var activityResultLauncher: ActivityResultLauncher<Intent>? = null
    private var activity: FragmentActivity? = null
    protected var isNavigationPageActive: Boolean = false
    protected var isDisplayInitialized: Boolean = false
    private var riderServiceConfig: RiderServiceConfig = RiderServiceConfig(
        isSupportDvr = false,
        isSupportNavi = false,
        isSupportScreenNavi = false,
        isSupportWeather = false,
        isSupportNotification = false,
        isSupportCircularScreen = false,
        isSupportCruise = false,
        isSupportMirror = false
    )

    // 导航模式相关变量
    protected var isMirrorModeActive = false
    protected var previousNavigationMode: NaviMode = NaviMode.NoNavi
    protected var currentNavigationMode: NaviMode = NaviMode.Default

    // UI相关变量
    var selectDialogBuilder: SelectDialog.Builder? = null

    /**
     * 设置 RiderService 配置
     */
    fun setRiderServiceConfig(riderServiceConfig: RiderServiceConfig) {
        this.riderServiceConfig = riderServiceConfig
    }

    /**
     * 应用主题模式设置，统一处理日/夜间模式切换
     */
    protected fun applyThemeMode(
        context: Context,
        nightMode: Boolean,
        updateFollowSystemToFalseForDayMode: Boolean = false
    ) {
        val preference =
            context.getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
        if (nightMode) {
            ThemeManager.themeMode = ThemeManager.ThemeMode.NIGHT
            preference.edit {
                putBoolean("mode_night", true)
            }
        } else { // Day mode
            ThemeManager.themeMode = ThemeManager.ThemeMode.DAY
            preference.edit {
                putBoolean("mode_night", false)
                if (updateFollowSystemToFalseForDayMode) {
                    putBoolean("follow_system", false)
                }
            }
        }
    }

    //------------------------------- 导航模式管理 ------------------------------//

    /**
     * 设置是否处于导航页面
     */
    fun setIsNaviPage(isNavigationPageActive: Boolean) {
        this.isNavigationPageActive = isNavigationPageActive
    }

    /**
     * 停止导航并更新导航模式
     */
    fun stopNavi() {
        currentNavigationMode = if (riderServiceConfig.isSupportCruise) {
            NaviMode.CruiseNAVI
        } else {
            NaviMode.NoNavi
        }

        if (previousNavigationMode is NaviMode.MirrorNAVI) {
            currentNavigationMode = NaviMode.MirrorNAVI
        }

        onStopNavi()
        sendNaviModeStop(previousNavigationMode)
    }

    /**
     * 开始导航并更新导航模式
     */
    fun startNavi() {
        Log.d(getTag(), "startNavi ")
        if (isNavigationPageActive) {
            requiresNavigationStart = true
            sendNaviModeStop(previousNavigationMode)

            when (previousNavigationMode) {
                is NaviMode.MirrorNAVI -> {
                    currentNavigationMode = NaviMode.MirrorNAVI
                    return
                }

                is NaviMode.LockScreenNavi -> {
                    currentNavigationMode = NaviMode.LockScreenNavi
                    return
                }

                else -> {
                    currentNavigationMode = NaviMode.Default
                }
            }
        }
    }

    /**
     * 向RiderService发送导航模式停止命令
     */
    protected fun sendNaviModeStop(newNavigationMode: NaviMode) {
        Log.d(getTag(), "sendNaviModeStop: $newNavigationMode")
        if (isMirrorModeActive && previousNavigationMode is NaviMode.MirrorNAVI) {
            RiderService.instance.sendNaviModeStop(NaviMode.MirrorNAVI)
            return
        }
        RiderService.instance.sendNaviModeStop(newNavigationMode)
    }

    /**
     * 设置导航模式
     */
    protected fun setNaviMode(newNavigationMode: NaviMode) {
        RiderService.instance.setNaviMode(newNavigationMode)
    }

    /**
     * 转换到锁屏导航模式
     */
    @Synchronized
    protected fun convertToLockScreenNavi() {
        if (currentNavigationMode == NaviMode.MirrorNAVI) {
            setNaviMode(NaviMode.LockScreenNavi)
            currentNavigationMode = NaviMode.LockScreenNavi
            sendNaviModeStop(previousNavigationMode)
            RiderService.instance.requestLockScreenDisplay()
        }
    }

    /**
     * 转换到地图导航模式
     */
    @Synchronized
    protected fun convertToMirrorNavi() {
        if (currentNavigationMode == NaviMode.LockScreenNavi) {
            setNaviMode(NaviMode.MirrorNAVI)
            currentNavigationMode = NaviMode.MirrorNAVI
            sendNaviModeStop(previousNavigationMode)
        }
    }

    /**
     * 转换到地图模式
     */
    @Synchronized
    protected fun convertToCruiseNAVI() {
        if (currentNavigationMode == NaviMode.LockScreenNavi) {
            setNaviMode(NaviMode.CruiseNAVI)
            currentNavigationMode = NaviMode.CruiseNAVI
            sendNaviModeStop(previousNavigationMode)
        }
    }

    //------------------------------- 服务回调实现 ------------------------------//

    protected val connectCallback = object : RiderServiceCallback() {
        override fun onNaviModeStartResponse(newNavigationMode: NaviMode) {
            super.onNaviModeStartResponse(newNavigationMode)
            Log.d(getTag(), "onNaviModeStartResponse: $newNavigationMode")
            RiderService.instance.sendNaviModeChange(newNavigationMode)
        }

        override fun onNaviModeStopResponse(newNavigationMode: NaviMode) {
            super.onNaviModeStopResponse(newNavigationMode)
            Log.d(getTag(), "onNaviModeStopResponse: $newNavigationMode")
            RiderService.instance.sendNaviModeStart(currentNavigationMode)
        }

        override fun onNaviModeChange(newNavigationMode: NaviMode) {
            super.onNaviModeChange(newNavigationMode)
            Log.d(getTag(), "onNaviModeChange:$newNavigationMode")
            currentNavigationMode = newNavigationMode
            sendNaviModeStop(previousNavigationMode)
        }

        override fun onNaviModeChangeResponse(navigationMode: NaviMode, ready: Boolean) {
            super.onNaviModeChangeResponse(navigationMode, ready)
            Log.d(getTag(), "onNaviModeChangeResponse: $navigationMode ")
            if (ready) {
                if (requiresNavigationStart) {
                    requiresNavigationStart = false
                    onStartNavi()
                }
                previousNavigationMode = navigationMode
                RiderService.instance.sendNaviModeChangeResponse(NaviModeChangeResult.NAVI_MODE_CHANGE_SUCCESS)
            }
        }

        override fun onVideoChannelReady() {
            super.onVideoChannelReady()
            Log.d(getTag(), "onVideoChannelReady")
            if (riderServiceConfig.isSupportCruise) {
                if (previousNavigationMode == NaviMode.NoNavi) {
                    currentNavigationMode = NaviMode.CruiseNAVI
                }
                sendNaviModeStop(previousNavigationMode)
            }
        }

        override fun onDisplayInitialized(display: Display) {
            super.onDisplayInitialized(display)
            Log.e(getTag(), "onDisplayInitialized: $currentNavigationMode")
            when (currentNavigationMode) {
                NaviMode.CruiseNAVI, NaviMode.ScreenNavi, NaviMode.Default -> startNaviScreenProjection(
                    display
                )

                NaviMode.LockScreenNavi -> startLockScreenProjection(display)
                else -> {}
            }
        }

        override fun onDisplayReleased(display: Display) {
            super.onDisplayReleased(display)
            isDisplayInitialized = false
            Log.d(getTag(), "onDisplayReleased")
        }

        override fun onRequestMediaProjection() {
            super.onRequestMediaProjection()
            Log.d(getTag(), "onRequestMediaProjection")
            val currentActivity = activity
            if (currentActivity != null && AppBackgroundManager.getInstance().isAppOnForeground()) {
                onRequestMediaProjectionImpl(currentActivity)
            } else {
                convertToLockScreenNavi()
            }
        }

        override fun onMirrorStart() {
            super.onMirrorStart()
            Log.d(getTag(), "onMirrorStart: ")
            isMirrorModeActive = true
        }

        override fun onMirrorStop() {
            super.onMirrorStop()
            Log.d(getTag(), "onMirrorStop: ")
            isMirrorModeActive = false
        }

        override fun onEnableNotificationFailed(device: BluetoothDevice, status: Int) {
            super.onEnableNotificationFailed(device, status)
            val currentActivity = activity ?: return

            if (selectDialogBuilder?.isShowing() != false) {
                selectDialogBuilder = SelectDialog.Builder(currentActivity)
                    .setTitle(currentActivity.getString(com.link.riderlink.R.string.error_title))
                    .setMessage(currentActivity.getString(com.link.riderlink.R.string.error_message) + "$status")
                    .setSelectViewVisibility(View.GONE)
                    .setCanceledOnTouchOutside(true)
                selectDialogBuilder?.show()
            }
        }

        override fun onConnectStatusChange(status: Connection) {
            super.onConnectStatusChange(status)
            if (status.btStatus is BleStatus.DeviceConnected) {
                if (selectDialogBuilder?.isShowing() != true) {
                    selectDialogBuilder?.dismiss()
                }
            }
        }

        override fun onDialogShow(title: String, message: String) {
            super.onDialogShow(title, message)
            val currentActivity = activity ?: return

            mainScope.launch {
                if (selectDialogBuilder?.isShowing() == true) {
                    if (selectDialogBuilder?.getTitle() == title && selectDialogBuilder?.getMessage() == message) {
                        return@launch
                    }
                    selectDialogBuilder?.dismiss()
                }
                selectDialogBuilder = SelectDialog.Builder(currentActivity)
                    .setTitle(title).setMessage(message)
                    .setSelectViewVisibility(View.GONE)
                    .setCanceledOnTouchOutside(true)
                selectDialogBuilder?.show()
            }
        }

        @SuppressLint("MissingPermission")
        override fun onRequestOpenBluetooth() {
            super.onRequestOpenBluetooth()
            val enableIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            activity?.startActivity(enableIntent)
        }

        override fun changeMap(type: Int) {
            super.changeMap(type)
            onChangeMapType(type)
        }
    }

    //------------------------------- 初始化和生命周期 ------------------------------//

    /**
     * 初始化 RiderLink
     */
    fun init() {
        addConnectCallback(connectCallback)
        addMapCallback()

        // 设置导航类型
        val preference = RiderService.instance.getApplication()
            .getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
        val isSimulationModeEnabled = preference.getBoolean("simulation_sw", false)
        setNaviType(isSimulationModeEnabled)

        // 添加屏幕状态监听
        setupScreenStateListeners()

        // 添加应用前后台状态监听
        setupAppStateListener()
    }

    /**
     * 设置屏幕状态监听器
     */
    private fun setupScreenStateListeners() {
        ScreenBrightnessUtils.addScreenListener(object :
            ScreenBrightnessUtils.OnScreenStateUpdateListener {
            override fun whenScreenOff() {
                Log.d(getTag(), "whenScreenOff $previousNavigationMode  $currentNavigationMode")
                convertToLockScreenNavi()
            }

            override fun whenScreenOn() {
                Log.d(getTag(), "whenScreenOn $previousNavigationMode  $currentNavigationMode")
            }

            override fun whenUserPresent() {
                Log.d(getTag(), "whenUserPresent $previousNavigationMode  $currentNavigationMode")
                convertToCruiseNAVI()
            }
        })
        ScreenBrightnessUtils.registerScreenBroadcastReceiver(AutoSizeUtils.getApplicationByReflect())
    }

    /**
     * 设置应用状态监听器
     */
    private fun setupAppStateListener() {
        AppBackgroundManager.getInstance()
            .regAppStateListener(object : AppBackgroundManager.IAppStateChangeListener {
                override fun onAppStateChanged(isAppForeground: Boolean) {
                    Log.d(getTag(), "onAppStateChanged: $isAppForeground")
                    if (isAppForeground) {
                        convertToCruiseNAVI()
                    }
                }
            })
    }

    /**
     * 销毁 RiderLink，释放资源
     */
    fun destroy() {
        onDestroy()
        RiderService.instance.destroy()
        ScreenBrightnessUtils.unregisterScreenBroadcastReceiver(AutoSizeUtils.getApplicationByReflect())
    }

    //------------------------------- 投影和显示管理 ------------------------------//

    /**
     * 开始导航屏幕投影
     */
    private fun startNaviScreenProjection(display: Display) {
        Log.d(getTag(), "onDisplayInitialized: $riderServiceConfig $display")
        if (riderServiceConfig.isSupportCruise && !isDisplayInitialized) {
            initNaviScreenProjection(display, riderServiceConfig.isSupportCircularScreen)
            isDisplayInitialized = true
        }
    }

    /**
     * 开始锁屏投影
     */
    private fun startLockScreenProjection(display: Display) {
        mainScope.launch {
            RiderService.instance.initLockScreenProjection(display)
        }
    }

    /**
     * 设置媒体投影
     */
    fun setMediaProjection(mediaProjection: MediaProjection) {
        RiderService.instance.setMediaProjection(mediaProjection)
    }

    //------------------------------- 镜像功能 ------------------------------//

    /**
     * 开始镜像功能
     */
    fun startMirror(launcher: ActivityResultLauncher<Intent>) {
        Log.d(getTag(), "startMirror: ")
        setNaviMode(NaviMode.MirrorNAVI)
        activityResultLauncher = launcher
        currentNavigationMode = NaviMode.MirrorNAVI
        sendNaviModeStop(previousNavigationMode)
    }

    /**
     * 停止镜像功能
     */
    fun stopMirror() {
        currentNavigationMode = if (riderServiceConfig.isSupportCruise) {
            NaviMode.CruiseNAVI
        } else {
            NaviMode.NoNavi
        }
        setNaviMode(currentNavigationMode)
        sendNaviModeStop(previousNavigationMode)
    }

    /**
     * 检查是否正在镜像
     */
    fun isMirroring(): Boolean {
        return isMirrorModeActive
    }

    //------------------------------- 活动和界面管理 ------------------------------//

    /**
     * 设置关联的活动实例
     */
    fun setActivity(activity: FragmentActivity?) {
        this.activity = activity
    }

    /**
     * 获取关联的活动实例
     */
    fun getActivity(): FragmentActivity? {
        return activity
    }

    /**
     * 处理 WiFi 开启事件
     */
    fun onWifiOpened() {
        if (selectDialogBuilder?.isShowing() == true) {
            selectDialogBuilder?.dismiss()
        }
    }

    //------------------------------- 蓝牙连接管理 ------------------------------//

    /**
     * 移除连接回调
     */
    fun removeConnectCallback(connectCallback: RiderServiceCallback) {
        RiderService.instance.removeCallback(connectCallback)
    }

    /**
     * 添加连接回调
     */
    fun addConnectCallback(connectCallback: RiderServiceCallback) {
        RiderService.instance.addCallback(connectCallback)
    }

    /**
     * 获取当前连接状态
     */
    fun getConnectStatus(): Connection {
        return RiderService.instance.getConnectStatus()
    }

    /**
     * 获取当前连接的蓝牙设备
     */
    fun getCurrentConnectDevice(): BleDevice? {
        return RiderService.instance.getCurrentConnectDevice()
    }

    /**
     * 连接蓝牙设备
     */
    fun connect(device: BleDevice) {
        RiderService.instance.connect(device)
    }

    /**
     * 开始蓝牙扫描
     */
    fun startBleScan() {
        RiderService.instance.startBleScan()
    }

    /**
     * 停止蓝牙扫描
     */
    fun stopBleScan() {
        RiderService.instance.stopBleScan()
    }

    /**
     * 断开蓝牙连接
     */
    fun disconnect(isManual: Boolean = true) {
        RiderService.instance.disconnect(isManual)
    }

    /**
     * 发送消息到 RiderService
     */
    fun sendMessageToRiderService(msg: RiderMessage) {
        RiderService.instance.sendMessageToRiderService(msg)
    }

    /**
     * 切换虚拟导航地图类型
     */
    fun changeVirtualNaviMap(type: Int, enabled: Boolean) {
        onChangeVirtualNaviMap(type, enabled)
    }

    /**
     * 设置导航类型
     */
    fun setNaviType(isSimulationMode: Boolean) {
        onSetNaviType(isSimulationMode)
    }

    //------------------------------- 抽象方法（子类实现） ------------------------------//

    /**
     * 获取日志标签
     */
    protected abstract fun getTag(): String

    /**
     * 添加地图回调
     */
    protected abstract fun addMapCallback()

    /**
     * 添加地图回调的公开方法
     */
    fun addMapCallback(mapCallback: Any) {
        onAddExternalMapCallback(mapCallback)
    }

    /**
     * 移除地图回调的公开方法
     */
    fun removeMapCallback(mapCallback: Any) {
        onRemoveExternalMapCallback(mapCallback)
    }

    /**
     * 地图相关的停止导航
     */
    protected abstract fun onStopNavi()

    /**
     * 地图相关的开始导航
     */
    protected abstract fun onStartNavi()

    /**
     * 初始化导航屏幕投影
     */
    protected abstract fun initNaviScreenProjection(
        display: Display,
        isSupportCircularScreen: Boolean
    )

    /**
     * 请求媒体投影的具体实现
     */
    protected abstract fun onRequestMediaProjectionImpl(activity: FragmentActivity)

    /**
     * 地图类型变化处理
     */
    protected abstract fun onChangeMapType(type: Int)

    /**
     * 销毁地图相关资源
     */
    protected abstract fun onDestroy()

    /**
     * 切换虚拟导航地图类型的具体实现
     */
    protected abstract fun onChangeVirtualNaviMap(type: Int, enabled: Boolean)

    /**
     * 设置导航类型的具体实现
     */
    protected abstract fun onSetNaviType(isSimulationMode: Boolean)

    /**
     * 添加外部地图回调的具体实现
     */
    protected abstract fun onAddExternalMapCallback(mapCallback: Any)

    /**
     * 移除外部地图回调的具体实现
     */
    protected abstract fun onRemoveExternalMapCallback(mapCallback: Any)
} 