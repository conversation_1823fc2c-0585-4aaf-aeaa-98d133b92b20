package com.link.riderlink.core.shared.search.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.link.riderlink.core.database.model.SearchAddress
import com.link.riderlink.core.shared.search.data.repository.SearchRepositoryImpl
import com.link.riderlink.core.shared.search.data.source.local.LocalSearchDataSourceImpl
import com.link.riderlink.ui.extensions.SharedFlowEvents
import com.link.riderlink.ui.extensions.setState
import com.link.riderlink.ui.extensions.setEvent
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 搜索功能的基础 ViewModel，负责处理搜索相关的公共业务逻辑
 * 子类需要实现具体的搜索API调用
 */
abstract class BaseSearchViewModel : ViewModel() {
    private val viewStateInternal = MutableStateFlow(BaseSearchViewState())
    val viewStates = viewStateInternal.asStateFlow()

    private val viewEventsInternal = SharedFlowEvents<BaseSearchViewEvent>()
    val viewEvents = viewEventsInternal.asSharedFlow()

    private val searchRepository = SearchRepositoryImpl(LocalSearchDataSourceImpl())
    
    companion object {
        protected const val TAG = "BaseSearchViewModel"
    }

    /**
     * 抽象方法：执行具体的搜索操作
     * 子类需要实现此方法来调用相应地图SDK的搜索API
     */
    protected abstract suspend fun performSearchApi(keyword: String): List<SearchAddress>

    /**
     * 处理视图动作
     */
    fun dispatch(viewAction: BaseSearchViewAction) {
        when (viewAction) {
            is BaseSearchViewAction.LoadHistories -> loadHistories()
            is BaseSearchViewAction.InsertHistory -> insertHistory(viewAction.searchAddress)
            is BaseSearchViewAction.DeleteHistory -> deleteHistory(viewAction.searchAddress)
            is BaseSearchViewAction.Search -> performSearch(viewAction.keyword)
        }
    }

    /**
     * 加载历史记录
     */
    private fun loadHistories() {
        viewModelScope.launch {
            try {
                searchRepository.loadHistories().collect { histories ->
                    viewStateInternal.setState { copy(histories = histories) }
                }
            } catch (e: Exception) {
                handleError("加载历史记录失败: ${e.message}")
            }
        }
    }

    /**
     * 插入历史记录
     */
    private fun insertHistory(searchAddress: SearchAddress) {
        viewModelScope.launch {
            try {
                searchRepository.insertHistory(searchAddress)
            } catch (e: Exception) {
                handleError("保存历史记录失败: ${e.message}")
            }
        }
    }

    /**
     * 删除历史记录
     */
    private fun deleteHistory(searchAddress: SearchAddress) {
        viewModelScope.launch {
            try {
                searchRepository.deleteHistory(searchAddress)
            } catch (e: Exception) {
                handleError("删除历史记录失败: ${e.message}")
            }
        }
    }

    /**
     * 执行搜索操作
     */
    private fun performSearch(keyword: String) {
        if (keyword.isBlank()) return
        
        viewModelScope.launch {
            viewStateInternal.setState { copy(isLoading = true, error = null) }
            
            try {
                val searchResults = performSearchApi(keyword)
                viewStateInternal.setState {
                    copy(
                        result = searchResults,
                        isLoading = false
                    )
                }
                viewEventsInternal.setEvent(BaseSearchViewEvent.SearchCompleted)
            } catch (e: Exception) {
                handleError("搜索失败: ${e.message}")
            }
        }
    }

    /**
     * 处理错误
     */
    private fun handleError(message: String) {
        viewModelScope.launch {
            viewStateInternal.setState { 
                copy(isLoading = false, error = message) 
            }
            viewEventsInternal.setEvent(BaseSearchViewEvent.ShowError(message))
        }
    }
} 