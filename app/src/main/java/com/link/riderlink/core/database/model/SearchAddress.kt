package com.link.riderlink.core.database.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * <AUTHOR>
 * @date 2022/6/29
 */

@Entity(tableName = "history")
data class SearchAddress(
    @PrimaryKey(autoGenerate = true) var id: Int = 0,
    @ColumnInfo val ad_code: String = "0",
    @ColumnInfo val district: String = "0",
    @ColumnInfo val name: String = "",
    @ColumnInfo val poi_id: String = "",
    @ColumnInfo val point_latitude: String = "0.0",
    @ColumnInfo val point_longitude: String = "0.0",
    @ColumnInfo val type: Int = 0
)