package com.link.riderlink.core.shared.search.data.repository

import com.link.riderlink.core.database.model.SearchAddress
import com.link.riderlink.core.shared.search.domain.repository.SearchRepository
import com.link.riderlink.core.shared.search.data.source.local.LocalSearchDataSource
import kotlinx.coroutines.flow.Flow

/**
 * 搜索仓库实现类，负责管理搜索历史记录等数据
 * 
 * @property localSearchDataSource 本地搜索数据源
 */
class SearchRepositoryImpl(
    private val localSearchDataSource: LocalSearchDataSource
) : SearchRepository {
    /**
     * 插入搜索历史记录
     * 
     * @param history 要插入的历史记录
     */
    override suspend fun insertHistory(history: SearchAddress) {
        localSearchDataSource.insertHistory(history)
    }

    /**
     * 删除搜索历史记录
     * 
     * @param history 要删除的历史记录
     */
    override suspend fun deleteHistory(history: SearchAddress) {
        localSearchDataSource.deleteHistory(history)
    }

    /**
     * 加载所有历史记录
     * 
     * @return 历史记录列表的流
     */
    override fun loadHistories(): Flow<List<SearchAddress>> = localSearchDataSource.loadHistories()
}