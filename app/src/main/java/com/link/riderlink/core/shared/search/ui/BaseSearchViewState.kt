package com.link.riderlink.core.shared.search.ui

import com.link.riderlink.core.database.model.SearchAddress

/**
 * 搜索页面的基础视图状态
 * 
 * @property histories 历史搜索记录列表
 * @property result 搜索结果列表
 * @property isLoading 是否正在加载
 * @property error 错误信息
 */
data class BaseSearchViewState(
    val histories: List<SearchAddress> = emptyList(),
    val result: List<SearchAddress> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null
)

/**
 * 搜索页面的基础事件
 */
sealed class BaseSearchViewEvent {
    data class ShowError(val message: String) : BaseSearchViewEvent()
    object SearchCompleted : BaseSearchViewEvent()
}

/**
 * 搜索页面的基础操作
 */
sealed class BaseSearchViewAction {
    /**
     * 搜索操作
     * @param keyword 搜索关键词
     */
    data class Search(val keyword: String) : BaseSearchViewAction()
    
    /**
     * 插入历史记录操作
     * @param searchAddress 要插入的地址
     */
    data class InsertHistory(val searchAddress: SearchAddress) : BaseSearchViewAction()
    
    /**
     * 删除历史记录操作
     * @param searchAddress 要删除的地址
     */
    data class DeleteHistory(val searchAddress: SearchAddress) : BaseSearchViewAction()
    
    /**
     * 加载历史记录操作
     */
    object LoadHistories : BaseSearchViewAction()
} 