package com.link.riderlink.core.database.dao

import androidx.room.*
import com.link.riderlink.core.database.model.SearchAddress
import kotlinx.coroutines.flow.Flow

/**
 * <AUTHOR>
 * @date 2022/6/29
 */

@Dao
interface UserHistoryDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHistory(searchAddress: SearchAddress)

    @Delete
    suspend fun deleteHistory(searchAddress: SearchAddress)

    @Query("SELECT * FROM history ORDER BY id DESC")
    fun loadHistories(): Flow<List<SearchAddress>>
}