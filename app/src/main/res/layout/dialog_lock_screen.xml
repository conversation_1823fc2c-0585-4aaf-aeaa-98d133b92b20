<?xml version="1.0" encoding="utf-8"?>
<com.link.riderlink.ui.components.LockScreenView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/lock_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:alpha="0.9"
    android:background="#000000">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="116dp"
        android:src="@drawable/scrollup"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:layout_marginBottom="80dp"
        android:text="@string/slide_up_text"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</com.link.riderlink.ui.components.LockScreenView>
