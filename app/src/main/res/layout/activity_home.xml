<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".features.MainActivity">

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/nav_host_fragment"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:defaultNavHost="true"
        app:navGraph="@navigation/nav_graph" />

    <com.link.riderlink.ui.components.DragFloatActionButton
        android:id="@+id/fab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|bottom"
        android:layout_marginBottom="400dp"
        android:scaleType="centerCrop"
        android:src="@drawable/screen_lock"
        android:visibility="gone" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/launch_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/launch_page">

        <ImageView
            android:id="@+id/launch_img"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="250dp"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            android:src="@drawable/start_main"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="86dp"
            android:layout_height="25dp"
            android:layout_marginBottom="71dp"
            android:gravity="center"
            android:text="@string/app_name"
            android:textColor="#5C7BD7"
            android:textSize="18sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>