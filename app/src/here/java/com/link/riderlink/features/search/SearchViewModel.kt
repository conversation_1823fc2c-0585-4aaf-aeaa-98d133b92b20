package com.link.riderlink.features.search

import com.link.riderhere.api.dto.SearchResult
import com.link.riderlink.RiderLink
import com.link.riderlink.core.database.model.SearchAddress
import com.link.riderlink.core.shared.search.ui.BaseSearchViewModel

/**
 * Here地图搜索ViewModel实现
 * 继承BaseSearchViewModel，只需实现具体的搜索API调用
 */
class SearchViewModel : BaseSearchViewModel() {

    /**
     * 实现基类的抽象方法：执行Here地图搜索API调用
     */
    override suspend fun performSearchApi(keyword: String): List<SearchAddress> {
        val result: SearchResult = RiderLink.instance.search(keyword)
        return result.searchList?.mapNotNull { searchItem ->
            searchItem.geoCoordinates?.let {
                SearchAddress(
                    ad_code = searchItem.address.countryCode,
                    district = searchItem.address.district,
                    name = searchItem.title,
                    poi_id = searchItem.address.postalCode,
                    point_latitude = it.latitude.toString(),
                    point_longitude = it.longitude.toString(),
                )
            }
        } ?: emptyList()
    }
}