package com.link.riderlink.features.search

import androidx.fragment.app.viewModels
import com.link.riderlink.core.database.model.SearchAddress
import com.link.riderlink.core.shared.search.ui.BaseSearchDialogFragment
import com.link.riderlink.core.shared.search.ui.BaseSearchViewModel

/**
 * Here地图搜索对话框Fragment
 * 继承BaseSearchDialogFragment，只需提供具体的ViewModel实现
 */
class SearchDialogFragment(
    onAddressSelected: (SearchAddress) -> Unit
) : BaseSearchDialogFragment(onAddressSelected) {

    private val searchViewModel: SearchViewModel by viewModels()
    
    override fun createSearchViewModel(): BaseSearchViewModel = searchViewModel
    
    companion object {
        private const val TAG = "HereSearchDialogFragment"
    }
} 