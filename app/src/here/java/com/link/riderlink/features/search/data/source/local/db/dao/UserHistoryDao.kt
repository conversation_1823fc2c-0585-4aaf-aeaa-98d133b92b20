package com.link.riderlink.features.search.data.source.local.db.dao

import androidx.room.*
import com.link.riderlink.features.search.data.source.local.db.model.SearchAddress
import kotlinx.coroutines.flow.Flow

@Dao
interface UserHistoryDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHistory(searchAddress: SearchAddress)

    @Delete
    suspend fun deleteHistory(searchAddress: SearchAddress)

    @Query("SELECT * FROM history ORDER BY id DESC")
    fun loadHistories(): Flow<List<SearchAddress>>
}