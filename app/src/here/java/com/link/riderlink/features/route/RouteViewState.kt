package com.link.riderlink.features.route

import com.here.sdk.core.GeoCoordinates
import com.here.sdk.routing.Route
import com.link.riderservice.api.dto.Connection

sealed class NaviState {
    object NONE : NaviState()
    object STARTED : NaviState()
    object STOPPED : NaviState()
    object DESTINATION : NaviState()
}

data class RouteViewState(
    val navigationState: NaviState = NaviState.NONE,
    val routes: List<Route?> = emptyList(),
    val connectionStatus: Connection = Connection(),
    val isSupportScreening: Boolean = true,
    val isNavigationActive: Boolean = false
)

sealed class RouteViewEvent {
    data class CalculateRouteFail(val errorMessage: String) : RouteViewEvent()
    data class NaviDisplayTypeChanged(val type: Int) : RouteViewEvent()
}


sealed class RouteViewAction {
    object StopNavi : RouteViewAction()
    object StartNavi : RouteViewAction()
    data class CalculateRideRoute(val origin: GeoCoordinates, val destination: GeoCoordinates) :
        RouteViewAction()
}