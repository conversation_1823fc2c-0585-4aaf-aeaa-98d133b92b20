package com.link.riderlink.features.search.data.source.local.db.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "history")
data class SearchAddress(
    @PrimaryKey(autoGenerate = true) var id: Int = 0,
    @ColumnInfo val ad_code: String = "0",
    @ColumnInfo val district: String = "0",
    @ColumnInfo val name: String = "",
    @ColumnInfo val poi_id: String = "",
    @ColumnInfo val point_latitude: String = "0.0",
    @ColumnInfo val point_longitude: String = "0.0",
    @ColumnInfo val type: Int = 0
)