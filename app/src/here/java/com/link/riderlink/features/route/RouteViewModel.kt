package com.link.riderlink.features.route

import android.graphics.Color
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.here.sdk.core.GeoCoordinates
import com.here.sdk.routing.Route
import com.link.riderhere.api.MapCallback
import com.link.riderlink.RiderLink
import com.link.riderservice.api.dto.BleStatus
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.api.dto.WifiStatus
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import me.jessyan.autosize.AutoSizeConfig
import androidx.core.graphics.toColorInt
import com.link.riderlink.features.route.RouteViewEvent.NaviDisplayTypeChanged
import com.link.riderlink.ui.extensions.SharedFlowEvents
import com.link.riderlink.ui.extensions.setEvent
import com.link.riderlink.ui.extensions.setState
import com.link.riderlink.ui.extensions.userPreferencesDataStore
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderservice.api.callback.RiderServiceCallback

class RouteViewModel : ViewModel() {

    private val routeViewStateInternal = MutableStateFlow(RouteViewState())
    val viewStates = routeViewStateInternal.asStateFlow()

    private val routeViewEventInternal = SharedFlowEvents<RouteViewEvent>()
    val viewEvents = routeViewEventInternal.asSharedFlow()

    // 标记断开后是否需要恢复导航
    private var shouldResumeAfterReconnect = false

    fun dispatch(action: RouteViewAction) {
        when (action) {
            is RouteViewAction.StartNavi -> startNavi()
            is RouteViewAction.StopNavi -> {
                shouldResumeAfterReconnect = false
                stopNavi()
            }
            is RouteViewAction.CalculateRideRoute -> {
                viewModelScope.launch {
                    runCatching {
                        calculateRideRoute(action.origin, action.destination)
                    }.onSuccess { result ->
                        if (result.isEmpty()) {
                            routeViewEventInternal.setEvent(RouteViewEvent.CalculateRouteFail(ROUTE_FAIL_MSG))
                        } else {
                            routeViewStateInternal.setState { copy(routes = result) }
                        }
                    }.onFailure {
                        routeViewEventInternal.setEvent(RouteViewEvent.CalculateRouteFail(ROUTE_FAIL_MSG))
                    }
                }
            }
        }
    }

    private fun stopNavi() {
        RiderLink.instance.stopNavi()
    }

    private suspend fun calculateRideRoute(
        from: GeoCoordinates,
        to: GeoCoordinates
    ): List<Route?> {
        return RiderLink.instance.calculateDriveRoute(from, to)
    }

    private fun startNavi() {
        RiderLink.instance.startNavi()
    }

    /**
     * 地图导航相关回调，处理导航状态、UI主题等
     */
    private inner class NaviDataCallback : MapCallback() {
        override fun onStartNavi() {
            super.onStartNavi()
            Log.d(TAG, "onStartNavi")
            routeViewStateInternal.setState {
                copy(
                    navigationState = NaviState.STARTED,
                    isNavigationActive = true
                )
            }
            ThemeManager.showBar(true, "#040507".toColorInt())
        }

        override fun onStopNavi() {
            super.onStopNavi()
            Log.d(TAG, "onStopNavi")
            shouldResumeAfterReconnect = false
            routeViewStateInternal.setState {
                copy(
                    navigationState = NaviState.STOPPED,
                    isNavigationActive = false
                )
            }
            ThemeManager.showBar(true, ThemeManager.autoChangeInt(Color.TRANSPARENT, "#2A3042".toColorInt()))
        }

        override fun onArriveDestination() {
            super.onArriveDestination()
            shouldResumeAfterReconnect = false
            routeViewStateInternal.setState {
                copy(navigationState = NaviState.DESTINATION)
            }
        }

        override fun changeMap(type: Int) {
            super.changeMap(type)
            viewModelScope.launch {
                routeViewEventInternal.setEvent(NaviDisplayTypeChanged(type))
            }
        }
    }

    /**
     * 设备连接相关回调，处理蓝牙/WiFi断开重连后的导航恢复
     */
    private inner class ConnectCallback : RiderServiceCallback() {
        override fun onConnectStatusChange(status: Connection) {
            routeViewStateInternal.setState { copy(connectionStatus = status) }

            if (status.btStatus is BleStatus.DeviceDisconnected && routeViewStateInternal.value.navigationState == NaviState.STARTED) {
                shouldResumeAfterReconnect = true
                return
            }
            if (status.wifiStatus is WifiStatus.DeviceDisconnected && routeViewStateInternal.value.navigationState == NaviState.STARTED) {
                shouldResumeAfterReconnect = true
                return
            }
        }

        override fun onClusterReady() {
            super.onClusterReady()
            if (shouldResumeAfterReconnect) {
                startNavi()
            }
        }

        override fun onVideoChannelReady() {
            super.onVideoChannelReady()
            if (shouldResumeAfterReconnect) {
                startNavi()
            }
        }

        override fun changeMap(type: Int) {
            super.changeMap(type)
            viewModelScope.launch {
                routeViewEventInternal.setEvent(NaviDisplayTypeChanged(type))
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        RiderLink.instance.setIsNaviPage(false)
        RiderLink.instance.removeConnectCallback(connectCallback)
        RiderLink.instance.removeMapCallback(navigationDataCallback)
    }

    // 回调实例
    private val navigationDataCallback = NaviDataCallback()
    private val connectCallback = ConnectCallback()

    init {
        RiderLink.instance.setIsNaviPage(true)
        RiderLink.instance.addMapCallback(navigationDataCallback)
        RiderLink.instance.addConnectCallback(connectCallback)
        viewModelScope.launch {
            val isScreenNavigationSupported =
                AutoSizeConfig.getInstance().application.userPreferencesDataStore.data.map {
                    it.isSupportScreenNavi
                }.first()
            routeViewStateInternal.setState {
                copy(isSupportScreening = isScreenNavigationSupported)
            }
            RiderLink.instance.getConnectStatus().let {
                routeViewStateInternal.setState {
                    copy(connectionStatus = it)
                }
            }
        }
    }

    companion object {
        private const val TAG = "RouteViewModel"
        private const val ROUTE_FAIL_MSG = "路线规划失败"
    }
}