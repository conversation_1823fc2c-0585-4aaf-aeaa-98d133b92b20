package com.link.riderlink.features.map

import android.graphics.BitmapFactory
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import com.here.sdk.core.GeoCoordinates
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.hjq.toast.ToastUtils
import com.link.riderlink.R
import com.link.riderlink.databinding.FragmentMapBinding
import com.link.riderlink.features.search.SearchDialogFragment
import com.link.riderlink.ui.extensions.navigate
import com.link.riderlink.ui.extensions.observeEvent
import com.link.riderlink.ui.extensions.observeState
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.theme.setThemeBackgroundColor
import com.link.riderlink.utils.connectivity.isNetConnection
import com.link.riderlink.utils.system.PermissionsUtil
import com.link.riderlink.utils.system.locationPermissions

class MapFragment : Fragment() {
    private val viewModel: MapViewModel by viewModels()
    private var mapFragmentBinding: FragmentMapBinding? = null
    private val binding get() = mapFragmentBinding!!

    private val themeCallback = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            changeMap()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mapFragmentBinding = FragmentMapBinding.inflate(inflater, container, false)
        binding.simpleMap.onCreate(savedInstanceState)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.root.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            popBackStack()
        }
        binding.btnBack.setOnClickListener { popBackStack() }
        ThemeManager.registerThemeChangeListener(themeCallback)
        initMapListeners()
        initObservers()
    }

    private fun initMapListeners() {
        binding.simpleMap.setLocateClickListener {
            viewModel.dispatch(MapViewAction.StartLocation)
        }
        binding.simpleMap.setSearchClickListener { navigate() }
        binding.simpleMap.setOnMapLongClickListener { geoCoordinates ->
            if (!isNetConnection(context)) {
                ToastUtils.show(getString(R.string.no_network_message))
            }
            viewModel.dispatch(MapViewAction.GeocodeSearch(requireContext(), geoCoordinates))
        }
    }

    private fun initObservers() {
        viewModel.mapViewState.observeState(
            viewLifecycleOwner,
            MapViewState::mapState,
            state = Lifecycle.State.RESUMED
        ) {
            try {
                Log.d(TAG, "changeMap")
                binding.simpleMap.changeMap()
            } catch (exception: Exception) {
                Log.e(TAG, "changeMap error: $exception")
            }
        }

        viewModel.mapViewEvent.observeEvent(viewLifecycleOwner) { viewEvent ->
            when (viewEvent) {
                is MapViewEvent.UpdateSearchLocation -> {
                    val destination = viewEvent.destination
                    val geoCoordinates = GeoCoordinates(destination.latitude, destination.longitude)
                    binding.simpleMap.addMarker(
                        geoCoordinates,
                        BitmapFactory.decodeResource(resources, R.drawable.location)
                    )
                    binding.simpleMap.setSearchName(destination.address)
                }


                is MapViewEvent.UpdateCurrentPosition -> {
                    Log.d(TAG, "updateCurrentPosition")
                    viewEvent.location?.let { updateCurrentPosition(it.coordinates) }
                        ?: ToastUtils.show(getString(R.string.location_failed_message))
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        binding.simpleMap.onResume()
        requestLocation()
        Log.d(TAG, "onResume")
    }

    override fun onPause() {
        super.onPause()
        binding.simpleMap.onPause()
    }

    private fun requestLocation() {
        if (XXPermissions.isGranted(requireContext(), locationPermissions)) {
            viewModel.dispatch(MapViewAction.GetLocation)
        } else {
            val requestPermissionListener = {
                XXPermissions.with(requireContext())
                    .permission(Permission.ACCESS_FINE_LOCATION)
                    .permission(Permission.ACCESS_COARSE_LOCATION)
                    .request { _: List<String?>?, allPermissionsGranted: Boolean ->
                        if (allPermissionsGranted) {
                            viewModel.dispatch(MapViewAction.GetLocation)
                        } else {
                            ToastUtils.show(getString(R.string.no_permission_message))
                        }
                    }
            }
            PermissionsUtil.showRequestPermissionDialog(
                requireContext(),
                requestPermissionListener
            )
        }
    }

    private fun navigate() {
        if (binding.simpleMap.getSearchName().isNotEmpty()) {
            this.navigate(R.id.action_mapFragment_to_routeFragment)
            return
        }
        val searchRouteDialog = SearchDialogFragment {
            viewModel.dispatch(
                MapViewAction.OnRoute(
                    requireContext(),
                    it
                ) { this.navigate(R.id.action_mapFragment_to_routeFragment) }
            )
        }
        searchRouteDialog.show(childFragmentManager, "SearchDialog")
    }

    private fun updateCurrentPosition(geoCoordinates: GeoCoordinates) {
        binding.simpleMap.setPosition(geoCoordinates)
    }

    private fun changeMap() {
        binding.simpleMap.changeMap()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Log.d(TAG, "onDestroyView")
        binding.simpleMap.onDestroy()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        mapFragmentBinding = null
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
    }

    companion object {
        private const val TAG = "MapFragment"
    }
}