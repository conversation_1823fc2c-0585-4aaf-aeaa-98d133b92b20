package com.link.riderlink.features.search.data.source.local

import com.link.riderlink.database.AppDatabase
import com.link.riderlink.features.search.data.source.local.db.model.SearchAddress
import kotlinx.coroutines.flow.Flow
import me.jessyan.autosize.AutoSizeConfig

class LocalSearchDataSourceImpl : LocalSearchDataSource {
    private val appDatabase =
        AppDatabase.getInstance(AutoSizeConfig.getInstance().application.applicationContext)

    override suspend fun insertHistory(history: SearchAddress) {
        appDatabase.userHistoryDao().insertHistory(history)
    }

    override suspend fun deleteHistory(history: SearchAddress) {
        appDatabase.userHistoryDao().deleteHistory(history)
    }

    override fun loadHistories(): Flow<List<SearchAddress>> =
        appDatabase.userHistoryDao().loadHistories()

}