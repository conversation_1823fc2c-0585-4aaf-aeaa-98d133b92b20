package com.link.riderlink.features.search.domain.repository

import com.link.riderlink.features.search.data.source.local.db.model.SearchAddress
import kotlinx.coroutines.flow.Flow

/**
 * 搜索仓库接口，定义了搜索历史记录管理的基本操作
 */
interface SearchRepository {
    /**
     * 插入搜索历史记录
     *
     * @param history 要插入的历史记录
     */
    suspend fun insertHistory(history: SearchAddress)

    /**
     * 删除搜索历史记录
     *
     * @param history 要删除的历史记录
     */
    suspend fun deleteHistory(history: SearchAddress)

    /**
     * 加载所有历史记录
     *
     * @return 历史记录列表的流
     */
    fun loadHistories(): Flow<List<SearchAddress>>
}