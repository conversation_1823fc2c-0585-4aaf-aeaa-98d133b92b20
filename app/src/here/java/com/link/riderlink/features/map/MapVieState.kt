package com.link.riderlink.features.map

import android.content.Context
import com.here.sdk.core.GeoCoordinates
import com.here.sdk.core.Location
import com.link.riderlink.core.database.model.SearchAddress
import com.link.riderlink.utils.common.Destination

sealed class MapViewEvent {
    data class UpdateSearchLocation(val destination: Destination) : MapViewEvent()
    data class UpdateCurrentPosition(val location: Location?) : MapViewEvent()
}

data class MapViewState(
    val mapState: Int = 0
)

sealed class MapViewAction {
    data class GeocodeSearch(val context: Context, val geoCoordinates: GeoCoordinates) :
        MapViewAction()

    data class OnRoute(
        val context: Context,
        val searchAddress: SearchAddress,
        val callback: () -> Unit
    ) : MapViewAction()

    object GetLocation : MapViewAction()
    object StartLocation : MapViewAction()
}