package com.link.riderlink.features.home

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.link.riderlink.R
import com.link.riderlink.RiderLink
import com.link.riderlink.databinding.ConnectDialogListItemBinding
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.utils.connectivity.BTDistanceUtil
import com.link.riderservice.api.dto.BleStatus
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.api.dto.WifiStatus
import com.link.riderservice.feature.connection.ble.BleDevice

/**
 * 蓝牙设备列表适配器
 * 用于显示可连接的蓝牙设备列表，并处理设备的点击事件
 */
class DeviceAdapter(
    private val context: Context,
    private val click: (BleDevice) -> Unit,
    private val longClick: (View, BleDevice) -> Unit
) : ListAdapter<BleDevice, DeviceAdapter.DeviceViewHolder>(DIFF_CALLBACK) {

    // 设备列表项的颜色常量
    companion object {
        private const val TAG = "DeviceAdapter"
        private const val COLOR_NAME_DAY = "#202229"
        private const val COLOR_NAME_NIGHT = "#FFFFFF"
        private const val COLOR_STATE_CONNECTED = "#5C7BD7"
        private const val COLOR_STATE_DAY = "#8C909E"
        private const val COLOR_STATE_NIGHT = "#07C160"

        private val DIFF_CALLBACK: DiffUtil.ItemCallback<BleDevice> =
            object : DiffUtil.ItemCallback<BleDevice>() {
                override fun areItemsTheSame(
                    oldItem: BleDevice,
                    newItem: BleDevice
                ): Boolean {
                    return oldItem.device.address == newItem.device.address
                }

                override fun areContentsTheSame(
                    oldItem: BleDevice,
                    newItem: BleDevice
                ): Boolean {
                    return oldItem == newItem
                }
            }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceViewHolder {
        return DeviceViewHolder(
            ConnectDialogListItemBinding.inflate(
                LayoutInflater.from(parent.context), parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: DeviceViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * 设备列表项的ViewHolder
     */
    inner class DeviceViewHolder(private val binding: ConnectDialogListItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        /**
         * 绑定设备数据到视图
         */
        @SuppressLint("SetTextI18n")
        fun bind(device: BleDevice) = with(binding) {
            val isCurrentDevice = isCurrentConnectedDevice(device)
            val connection = RiderLink.instance.getConnectStatus()
            val isConnected = isFullyConnected(connection)

            // 设置基本信息
            setupBasicDeviceInfo(device, isCurrentDevice, connection)

            // 设置颜色和可见性
            setupColors(isCurrentDevice,isConnected)

            // 设置距离信息
            setupDistanceInfo(device)

            // 设置点击事件
            setupClickListeners(device)
        }

        /**
         * 设置设备基本信息
         */
        private fun setupBasicDeviceInfo(device: BleDevice, isCurrentDevice: Boolean, connection: Connection) {
            binding.apply {
                tvDeviceAddress.text = device.device.address
                tvDeviceName.text = device.completeLocalName() ?: context.getString(R.string.unknown)
                tvDeviceState.text = if (isCurrentDevice) getCurrentStatus(connection) else ""
            }
        }

        /**
         * 设置颜色和可见性
         */
        private fun setupColors(isCurrentDevice: Boolean,isConnected: Boolean) {
            binding.apply {
                // 设置名称颜色
                tvDeviceName.setTextColor(getDeviceNameColor())

                // 设置状态颜色和图标可见性
                tvDeviceState.setTextColor(getDeviceStateColor(isConnected && isCurrentDevice))
                imgDeviceState.visibility = if (isConnected && isCurrentDevice) View.VISIBLE else View.GONE
            }
        }

        /**
         * 设置距离信息
         */
        private fun setupDistanceInfo(device: BleDevice) {
            binding.tvDeviceDistance.text = formatDistance(device.rssi)
        }

        /**
         * 设置点击事件
         */
        private fun setupClickListeners(device: BleDevice) {
            binding.apply {
                dlDeviceItem.setOnClickListener { click(device) }
                dlDeviceItem.setOnLongClickListener {
                    longClick(it, device)
                    true
                }
            }
        }
    }

    /**
     * 格式化距离显示
     */
    private fun formatDistance(rssi: Int): String {
        val distance = BTDistanceUtil.getDistance(rssi)
        return context.getString(R.string.distance) + String.format("%.1f", distance) + "m"
    }

    /**
     * 获取设备名称颜色
     */
    private fun getDeviceNameColor(): Int {
        return ThemeColors.ADAPTER_TEXT.getCurrentColorInt(context)
    }

    /**
     * 获取设备状态颜色
     */
    private fun getDeviceStateColor(isConnected: Boolean): Int {
        return if (isConnected) {
            COLOR_STATE_CONNECTED.toColorInt()
        } else {
            ThemeColors.STATUS_ERROR.getCurrentColorInt(context)
        }
    }

    /**
     * 检查设备是否是当前连接的设备
     */
    private fun isCurrentConnectedDevice(device: BleDevice): Boolean {
        return device.device.address == RiderLink.instance.getCurrentConnectDevice()?.device?.address
    }

    /**
     * 检查是否完全连接（蓝牙和WiFi都已连接）
     */
    private fun isFullyConnected(connection: Connection): Boolean {
        return connection.btStatus is BleStatus.DeviceConnected &&
               connection.wifiStatus is WifiStatus.DeviceConnected
    }

    /**
     * 获取当前连接状态的文本描述
     */
    private fun getCurrentStatus(connection: Connection): String {
        return when (connection.btStatus) {
            is BleStatus.IDLE -> ""
            is BleStatus.DeviceConnected ->
                if (connection.wifiStatus is WifiStatus.DeviceConnected)
                    context.getString(R.string.connected)
                else
                    context.getString(R.string.connecting_status)
            else -> ""
        }
    }
}