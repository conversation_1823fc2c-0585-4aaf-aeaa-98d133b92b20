package com.link.riderlink.features.home

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.link.riderlink.RiderLink
import com.link.riderlink.ui.extensions.SharedFlowEvents
import com.link.riderlink.ui.extensions.setEvent
import com.link.riderlink.ui.extensions.setState
import com.link.riderservice.api.callback.RiderServiceCallback
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.feature.connection.ble.BleDevice
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlin.collections.toList

class DeviceViewModel(application: Application) : AndroidViewModel(application) {

    // 设备列表页面状态
    private val _viewStates = MutableStateFlow(DeviceViewState())
    val viewStates by lazy { _viewStates.asStateFlow() }

    // 事件流
    private val _viewEvents = SharedFlowEvents<BleViewEvent>()
    val viewEvents by lazy { _viewEvents.asSharedFlow() }

    /**
     * 处理页面动作
     */
    fun dispatch(viewAction: BleViewAction) = when (viewAction) {
        is BleViewAction.BleItemClicked -> RiderLink.instance.connect(viewAction.bleDevice)
        is BleViewAction.StartScanClicked -> RiderLink.instance.startBleScan()
        is BleViewAction.Disconnect -> RiderLink.instance.disconnect()
    }

    // 蓝牙相关回调
    private val bleCallback = object : RiderServiceCallback() {
        override fun onScanResult(devices: List<BleDevice>) {
            _viewStates.setState { copy(bleDevices = devices.toList()) }
        }

        override fun onScanning() {
            _viewStates.setState { copy(isScanning = true) }
        }

        override fun onScanFinish() {
            _viewStates.setState { copy(isScanning = false) }
        }

        override fun onConnectStatusChange(status: Connection) {
            viewModelScope.launch {
                _viewEvents.setEvent(BleViewEvent.ConnectStatusChange(status))
            }
        }
    }

    init {
        RiderLink.instance.addConnectCallback(bleCallback)
    }

    override fun onCleared() {
        RiderLink.instance.removeConnectCallback(bleCallback)
    }

    companion object {
        private const val TAG = "DeviceViewModel"
    }
}