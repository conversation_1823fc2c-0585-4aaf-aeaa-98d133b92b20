package com.link.riderlink.features.home

import com.link.riderservice.api.dto.Connection
import com.link.riderservice.feature.connection.ble.BleDevice

data class DeviceViewState(
    val bleDevices: List<BleDevice> = emptyList(),
    val isScanning: Boolean = false
)


sealed class BleViewEvent {
    data class ConnectStatusChange(val status: Connection) : BleViewEvent()
}

sealed class BleViewAction {
    data class BleItemClicked(val bleDevice: BleDevice) : BleViewAction()
    object StartScanClicked : BleViewAction()
    object Disconnect : BleViewAction()
}