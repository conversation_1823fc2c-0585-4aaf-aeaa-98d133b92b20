package com.link.riderlink.features.map

import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.here.sdk.core.GeoCoordinates
import com.here.sdk.search.Place
import com.link.riderhere.api.MapCallback
import com.link.riderlink.RiderLink
import com.link.riderlink.core.database.model.SearchAddress
import com.link.riderlink.ui.extensions.SharedFlowEvents
import com.link.riderlink.ui.extensions.saveDestination
import com.link.riderlink.ui.extensions.setEvent
import com.link.riderlink.ui.extensions.setState
import com.link.riderlink.utils.common.Destination
import com.link.riderservice.api.callback.RiderServiceCallback
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 地图视图模型，负责处理地图相关的状态管理和业务逻辑
 */
class MapViewModel : ViewModel() {
    companion object {
        private const val TAG = "MapViewModel"
    }

    private val mapViewStateInternal = MutableStateFlow(MapViewState())
    val mapViewState = mapViewStateInternal.asStateFlow()

    private val mapViewEventInternal = SharedFlowEvents<MapViewEvent>()
    val mapViewEvent = mapViewEventInternal.asSharedFlow()

    // 统一的异常处理
    private val exceptionHandler = CoroutineExceptionHandler { _, throwable ->
        Log.e(TAG, "操作异常: ${throwable.message}", throwable)
    }

    // 回调对象初始化
    private val connectCallback = object : RiderServiceCallback() {
        override fun changeMap(mapType: Int) {
            super.changeMap(mapType)
            mapViewStateInternal.setState { copy(mapState = mapType) }
        }
    }

    private val mapCallback = object : MapCallback() {
        override fun changeMap(mapType: Int) {
            super.changeMap(mapType)
            Log.d(TAG, "changeMap: mapCallback")
            mapViewStateInternal.setState { copy(mapState = mapType) }
        }
    }

    init {
        RiderLink.instance.apply {
            addConnectCallback(connectCallback)
            addMapCallback(mapCallback)
        }
    }

    override fun onCleared() {
        super.onCleared()
        RiderLink.instance.apply {
            removeConnectCallback(connectCallback)
            removeMapCallback(mapCallback)
        }
    }

    /**
     * 处理视图动作
     */
    fun dispatch(action: MapViewAction) {
        when (action) {
            is MapViewAction.GeocodeSearch -> handleGeoSearch(action.context, action.geoCoordinates)
            is MapViewAction.OnRoute -> handleRoute(
                action.context,
                action.searchAddress,
                action.callback
            )

            is MapViewAction.GetLocation -> getLocation()
            is MapViewAction.StartLocation -> startLocation()
        }
    }

    /**
     * 获取当前位置
     */
    private fun getLocation() {
        viewModelScope.launch(Dispatchers.IO + exceptionHandler) {
            val location = RiderLink.instance.getLbsLocation()
            Log.d(TAG, "updateLocation: $location")
            mapViewEventInternal.setEvent(MapViewEvent.UpdateCurrentPosition(location))
        }
    }

    /**
     * 启动位置追踪
     */
    private fun startLocation() {
        Log.d(TAG, "startLocation")
        viewModelScope.launch(Dispatchers.IO + exceptionHandler) {
            val location = RiderLink.instance.startLbsLocation()
            mapViewEventInternal.setEvent(MapViewEvent.UpdateCurrentPosition(location))
        }
    }

    /**
     * 处理长按地图地理位置搜索
     */
    private fun handleGeoSearch(context: Context, geoCoordinates: GeoCoordinates) {
        viewModelScope.launch(Dispatchers.IO + exceptionHandler) {
            try {
                val place = geocodeSearch(context, geoCoordinates)
                val destination = Destination(
                    latitude = geoCoordinates.latitude,
                    longitude = geoCoordinates.longitude,
                    address = place?.title ?: "",
                    district = place?.address?.district ?: ""
                )
                context.saveDestination(destination)
                Log.d(TAG, "地理编码搜索完成")
                place?.let {
                    mapViewEventInternal.setEvent(MapViewEvent.UpdateSearchLocation(destination))
                }
            } catch (exception: Exception) {
                Log.e(TAG, "地理编码搜索失败", exception)
            }
        }
    }

    /**
     * 设置路线
     */
    private fun handleRoute(context: Context, searchAddress: SearchAddress, callback: () -> Unit) {
        viewModelScope.launch(exceptionHandler) {
            try {
                val destination = Destination(
                    latitude = searchAddress.point_latitude.toDouble(),
                    longitude = searchAddress.point_longitude.toDouble(),
                    address = searchAddress.name,
                    district = searchAddress.district
                )
                context.saveDestination(destination)
                callback()
            } catch (exception: Exception) {
                Log.e(TAG, "设置路线失败", exception)
            }
        }
    }

    /**
     * 地理编码搜索
     */
    private suspend fun geocodeSearch(
        context: Context,
        geoCoordinates: GeoCoordinates
    ): Place? = RiderLink.instance.geocodeSearch(context, geoCoordinates)
}