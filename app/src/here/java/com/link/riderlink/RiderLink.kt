package com.link.riderlink

import android.content.Context
import android.media.projection.MediaProjectionManager
import android.util.Log
import android.view.Display
import androidx.fragment.app.FragmentActivity
import com.here.sdk.core.GeoCoordinates
import com.here.sdk.core.Location
import com.here.sdk.mapview.MapScheme
import com.here.sdk.routing.Route
import com.here.sdk.search.Place
import com.link.riderdvr.utils.mainScope
import com.link.riderhere.api.MapCallback
import com.link.riderhere.api.RiderMap
import com.link.riderhere.api.dto.NavigationInfo
import com.link.riderhere.api.dto.SearchResult
import com.link.riderhere.map.location.data.source.HereLocationListener
import com.link.riderlink.core.shared.riderlink.BaseRiderLink
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.dto.ArriveDestination
import com.link.riderservice.api.dto.NaviInfo
import com.link.riderservice.api.dto.NaviStart
import com.link.riderservice.api.dto.NaviStop
import com.link.riderservice.api.dto.NaviText
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

/**
 * Here地图实现的RiderLink
 * 继承自BaseRiderLink，实现Here地图特有的功能
 * 
 * RiderLink 类是应用程序与 RiderService 和地图功能的主要接口。
 * 负责管理导航模式、连接状态、地图交互等核心功能。
 */
class RiderLink : BaseRiderLink() {

    //------------------------------- Here地图特有的MapCallback实现 ------------------------------//

    private val mapCallback = object : MapCallback() {
        override fun onArriveDestination() {
            super.onArriveDestination()
            RiderService.instance.sendMessageToRiderService(ArriveDestination())
        }

        override fun onStartNavi() {
            super.onStartNavi()
            if (previousNavigationMode is com.link.riderservice.api.dto.NaviMode.MirrorNAVI) {
                return
            }
            RiderService.instance.sendMessageToRiderService(NaviStart())
        }

        override fun onStopNavi() {
            super.onStopNavi()
            if (previousNavigationMode is com.link.riderservice.api.dto.NaviMode.MirrorNAVI) {
                return
            }
            RiderService.instance.sendMessageToRiderService(NaviStop())
        }

        override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
            val naviInfo = NaviInfo(
                curLink = 0,
                curPoint = 0,
                curStep = 0,
                curStepRetainDistance = navigationInfo.curStepRetainDistance,
                curStepRetainTime = navigationInfo.curStepRetainTime,
                naviType = navigationInfo.naviType,
                pathRetainTime = navigationInfo.pathRetainTime,
                pathRetainDistance = navigationInfo.pathRetainDistance,
                routeRemainLightCount = navigationInfo.routeRemainLightCount,
                pathId = 0,
                nextRoadName = navigationInfo.nextRoadName,
                currentRoadName = navigationInfo.currentRoadName,
                iconType = navigationInfo.iconType,
                mapType = navigationInfo.mapType,
                turnIconName = navigationInfo.turnIconName,
                turnKind = ""
            )
            RiderService.instance.sendMessageToRiderService(naviInfo)
        }

        override fun onGetNavigationText(type: Int, text: String) {
            super.onGetNavigationText(type, text)
            val naviText = NaviText(type, text)
            RiderService.instance.sendMessageToRiderService(naviText)
        }

        override fun changeMap(type: Int) {
            super.changeMap(type)
            val context = RiderService.instance.getApplication()
            if (type == MapScheme.NORMAL_DAY.value) {
                applyThemeMode(context, nightMode = false, updateFollowSystemToFalseForDayMode = true)
            } else if (type == MapScheme.NORMAL_NIGHT.value) {
                applyThemeMode(context, nightMode = true)
            }
        }
    }

    //------------------------------- Here地图特有的公共方法 ------------------------------//

    /**
     * 获取位置信息
     */
    suspend fun getLbsLocation() = RiderMap.instance.getLbsLocation()

    /**
     * 开始位置更新，并返回首次获取的位置
     */
    suspend fun startLbsLocation(): Location =
        suspendCancellableCoroutine { continuation ->
            try {
                RiderMap.instance.startLbsLocation(object :
                    HereLocationListener {
                    override fun onLocationUpdated(location: Location) {
                        continuation.resume(location)
                        RiderMap.instance.stopLbsLocation()
                    }
                })
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

    /**
     * 地理编码搜索
     */
    suspend fun geocodeSearch(
        context: Context,
        geoCoordinates: GeoCoordinates
    ): Place? {
        return RiderMap.instance.geocodeSearch(context, geoCoordinates)
    }

    /**
     * 关键字搜索
     */
    suspend fun search(keyWord: String): SearchResult {
        return RiderMap.instance.search(keyWord)
    }

    /**
     * 计算驾驶路线
     */
    suspend fun calculateDriveRoute(
        from: GeoCoordinates,
        to: GeoCoordinates
    ): List<Route?> {
        return RiderMap.instance.calculateDriveRoute(from, to)
    }

    //------------------------------- 抽象方法实现 ------------------------------//

    override fun getTag(): String = TAG

    override fun addMapCallback() {
        RiderMap.instance.addCallback(mapCallback)
    }

    override fun onStopNavi() {
        RiderMap.instance.stopNavi()
    }

    override fun onStartNavi() {
        RiderMap.instance.startNavi()
    }

    override fun initNaviScreenProjection(display: Display, isSupportCircularScreen: Boolean) {
        mainScope.launch {
            RiderMap.instance.initNaviScreenProjection(display, isSupportCircularScreen)
        }
    }

    override fun onRequestMediaProjectionImpl(activity: FragmentActivity) {
        val mediaProjectionManager =
            activity.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        activityResultLauncher?.launch(mediaProjectionManager.createScreenCaptureIntent())
    }

    override fun onChangeMapType(type: Int) {
        val context = RiderService.instance.getApplication()
        if (type == MapScheme.NORMAL_DAY.value) {
            applyThemeMode(context, nightMode = false)
        } else if (type == MapScheme.NORMAL_NIGHT.value) {
            applyThemeMode(context, nightMode = true)
        }
    }

    override fun onDestroy() {
        RiderMap.instance.destroy()
    }

    override fun onChangeVirtualNaviMap(type: Int, enabled: Boolean) {
        RiderMap.instance.changeVirtualNaviMap(type, enabled)
    }

    override fun onSetNaviType(isSimulationMode: Boolean) {
        RiderMap.instance.setNaviType(isSimulationMode)
    }
    
    override fun onAddExternalMapCallback(mapCallback: Any) {
        if (mapCallback is com.link.riderhere.api.MapCallback) {
            RiderMap.instance.addCallback(mapCallback)
        }
    }
    
    override fun onRemoveExternalMapCallback(mapCallback: Any) {
        if (mapCallback is com.link.riderhere.api.MapCallback) {
            RiderMap.instance.removeCallback(mapCallback)
        }
    }

    companion object {
        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            RiderLink()
        }
        private const val TAG = "HereRiderLink"
    }
}